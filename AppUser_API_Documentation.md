# AppUser API Documentation

## Endpoints

### 1. Authentication Endpoints

#### Register User
```http
POST /api/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

#### Login User
```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

#### Logout User
```http
POST /api/auth/logout
Authorization: Bearer {token}
```

### 2. User Management Endpoints

#### Get All Users
```http
GET /api/appuser
Authorization: Bearer {token}
```

#### Get User by ID
```http
GET /api/appuser/{id}
Authorization: Bearer {token}
```

#### Get Current User Profile
```http
GET /api/appuser/profile
Authorization: Bearer {token}
```

#### Create New User
```http
POST /api/appuser
Authorization: Bearer {token}
Content-Type: application/json

{
  "firstName": "<PERSON>",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "password": "password123",
  "phone": "+84123456789",
  "bio": "Software Developer",
  "address": "123 Main St, City",
  "status": 0
}
```

#### Update User
```http
PUT /api/appuser/{id}
Authorization: Bearer {token}
Content-Type: application/json

{
  "firstName": "John",
  "lastName": "Doe",
  "phone": "+84123456789",
  "bio": "Senior Software Developer",
  "address": "456 New St, City",
  "status": 0
}
```

#### Update Current User Profile
```http
PUT /api/appuser/profile
Authorization: Bearer {token}
Content-Type: application/json

{
  "firstName": "John",
  "lastName": "Doe",
  "phone": "+84123456789",
  "bio": "Senior Software Developer",
  "address": "456 New St, City"
}
```

#### Change Password
```http
PUT /api/appuser/change-password
Authorization: Bearer {token}
Content-Type: application/json

{
  "currentPassword": "oldpassword123",
  "newPassword": "newpassword123",
  "confirmPassword": "newpassword123"
}
```

#### Delete User
```http
DELETE /api/appuser/{id}
Authorization: Bearer {token}
```

## Data Models

### UserStatus Enum
- `0` - Online
- `1` - Offline  
- `2` - Busy
- `3` - Away
- `4` - Banned

### AppUserDto Response
```json
{
  "id": 1,
  "firstName": "John",
  "lastName": "Doe",
  "fullName": "John Doe",
  "email": "<EMAIL>",
  "status": 0,
  "avatar": null,
  "phone": "+84123456789",
  "bio": "Software Developer",
  "address": "123 Main St, City",
  "lastActive": "2024-01-01T10:00:00Z",
  "lastLoginTime": "2024-01-01T09:00:00Z",
  "createdAt": "2024-01-01T08:00:00Z",
  "lastModifiedAt": "2024-01-01T10:30:00Z",
  "isActive": true
}
```

## Error Responses

### Common Error Codes
- `400` - Bad Request (Validation errors)
- `401` - Unauthorized (Invalid or missing token)
- `403` - Forbidden (Insufficient permissions)
- `404` - Not Found (User not found)
- `409` - Conflict (Email already exists)
- `500` - Internal Server Error

### Error Response Format
```json
{
  "isSuccess": false,
  "errorCode": "NotFound",
  "message": "Không tìm thấy người dùng",
  "data": null
}
```

## Validation Rules

### CreateAppUserRequest
- `firstName`: Required, max 100 characters
- `lastName`: Required, max 100 characters  
- `email`: Required, valid email format, max 255 characters
- `password`: Required, min 6 characters, max 100 characters
- `phone`: Optional, max 20 characters
- `bio`: Optional, max 500 characters
- `address`: Optional, max 255 characters

### UpdateAppUserRequest
- `firstName`: Required, max 100 characters
- `lastName`: Required, max 100 characters
- `phone`: Optional, max 20 characters
- `bio`: Optional, max 500 characters
- `address`: Optional, max 255 characters

### ChangePasswordRequest
- `currentPassword`: Required
- `newPassword`: Required, min 6 characters, max 100 characters
- `confirmPassword`: Required, must match newPassword

## Authentication

All endpoints except `/api/auth/register` and `/api/auth/login` require authentication.

### Token Authentication
Include the JWT token in the Authorization header:
```
Authorization: Bearer {your-jwt-token}
```

### Cookie Authentication
The API also supports cookie-based authentication. Tokens are automatically stored in httpOnly cookies after login.

## Features

- ✅ User registration and login
- ✅ JWT token authentication
- ✅ Cookie-based authentication
- ✅ Password hashing with BCrypt
- ✅ User profile management
- ✅ Password change functionality
- ✅ Soft delete for users
- ✅ Input validation with FluentValidation
- ✅ Current user context
- ✅ User status management
- ✅ Comprehensive error handling
