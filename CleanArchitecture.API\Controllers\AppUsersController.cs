using CleanArchitecture.API.Extension;
using CleanArchitectureAPI.Application.Interfaces;
using CleanArchitectureAPI.Application.DTOs.AppUser.Responses;
using CleanArchitectureAPI.Application.Requests.AppUser;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace CleanArchitecture.API.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class AppUsersController : ControllerBase
    {
        private readonly IAppUserServices _appUserService;

        public AppUsersController(IAppUserServices appUserService)
        {
            _appUserService = appUserService;
        }

        [HttpGet]
        public async Task<IActionResult> GetAllUsers()
        {
            var result = await _appUserService.GetAllAsync();
            return result.ToActionResult();
        }

        [HttpGet("project/{projectKey}/active")]
        public async Task<IActionResult> GetAllActiveUsersByProjectKey([FromRoute] string projectKey)
        {
            var result = await _appUserService.GetAllActiveByProjectKeyAsync(projectKey);
            return result.ToActionResult();
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetUserById(int id)
        {
            var result = await _appUserService.GetByIdAsync(id);
            return result.ToActionResult();
        }

        [HttpGet("profile")]
        public async Task<IActionResult> GetCurrentUser()
        {
            var result = await _appUserService.GetCurrentUserAsync();
            return result.ToActionResult();
        }

        [HttpPost]
        public async Task<IActionResult> CreateUser([FromBody] CreateAppUserRequest request)
        {
            var result = await _appUserService.CreateAsync(request);
            return result.ToActionResult();
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateUser(int id, [FromBody] UpdateAppUserRequest request)
        {
            var result = await _appUserService.UpdateAsync(id, request);
            return result.ToActionResult();
        }

        [HttpPut("profile")]
        public async Task<IActionResult> UpdateProfile([FromBody] UpdateProfileRequest request)
        {
            var currentUserResult = await _appUserService.GetCurrentUserAsync();
            if (!currentUserResult.IsSuccess)
                return currentUserResult.ToActionResult();

            var result = await _appUserService.UpdateProfileAsync(currentUserResult.Data!.Id, request);
            return result.ToActionResult();
        }

        [HttpPut("change-password")]
        public async Task<IActionResult> ChangePassword([FromBody] ChangePasswordRequest request)
        {
            var currentUserResult = await _appUserService.GetCurrentUserAsync();
            if (!currentUserResult.IsSuccess)
                return currentUserResult.ToActionResult();

            var result = await _appUserService.ChangePasswordAsync(currentUserResult.Data!.Id, request);
            return result.ToActionResult();
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteUser(int id)
        {
            var result = await _appUserService.DeleteAsync(id);
            return result.ToActionResult();
        }

        //get users by projectKey
        [HttpGet("project/{projectKey}")]
        public async Task<IActionResult> GetUsersByProjectKey([FromRoute] string projectKey)
        {
            var result = await _appUserService.GetUsersByProjectKeyAsync(projectKey);
            return result.ToActionResult();
        }
    }
}
