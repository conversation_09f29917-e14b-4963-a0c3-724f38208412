using CleanArchitectureAPI.Application.DTOs;
using CleanArchitectureAPI.Application.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace CleanArchitecture.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class IssueHistoryController : ControllerBase
    {
        private readonly IIssueHistoryService _issueHistoryService;

        public IssueHistoryController(IIssueHistoryService issueHistoryService)
        {
            _issueHistoryService = issueHistoryService;
        }

        #region Comments

        [HttpPost("comments")]
        public async Task<ActionResult<CommentDto>> CreateComment([FromBody] CreateCommentDto createCommentDto)
        {
            try
            {
                var comment = await _issueHistoryService.CreateCommentAsync(createCommentDto);
                return CreatedAtAction(nameof(GetComment), new { id = comment.Id }, comment);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("comments/{id}")]
        public async Task<ActionResult<CommentDto>> GetComment(int id)
        {
            var comment = await _issueHistoryService.GetCommentByIdAsync(id);
            if (comment == null)
                return NotFound();

            return Ok(comment);
        }

        [HttpGet("issues/{issueId}/comments")]
        public async Task<ActionResult<IEnumerable<CommentDto>>> GetCommentsByIssue(int issueId)
        {
            var comments = await _issueHistoryService.GetCommentsByIssueIdAsync(issueId);
            return Ok(comments);
        }

        [HttpGet("issues/{issueId}/comments/paged")]
        public async Task<ActionResult<CommentPagedResultDto>> GetCommentsPagedByIssue(
            int issueId, 
            [FromQuery] int pageNumber = 1, 
            [FromQuery] int pageSize = 10)
        {
            var result = await _issueHistoryService.GetCommentsPagedAsync(issueId, pageNumber, pageSize);
            return Ok(result);
        }

        [HttpPut("comments/{id}")]
        public async Task<ActionResult<CommentDto>> UpdateComment(int id, [FromBody] UpdateCommentDto updateCommentDto)
        {
            try
            {
                var comment = await _issueHistoryService.UpdateCommentAsync(id, updateCommentDto);
                return Ok(comment);
            }
            catch (ArgumentException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpDelete("comments/{id}")]
        public async Task<ActionResult> DeleteComment(int id)
        {
            var result = await _issueHistoryService.DeleteCommentAsync(id);
            if (!result)
                return NotFound();

            return NoContent();
        }

        #endregion

        #region Change Groups

        [HttpPost("change-groups")]
        public async Task<ActionResult<ChangeGroupDto>> CreateChangeGroup([FromBody] CreateChangeGroupDto createChangeGroupDto)
        {
            try
            {
                var changeGroup = await _issueHistoryService.CreateChangeGroupAsync(createChangeGroupDto);
                return CreatedAtAction(nameof(GetChangeGroup), new { id = changeGroup.Id }, changeGroup);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("change-groups/{id}")]
        public async Task<ActionResult<ChangeGroupDto>> GetChangeGroup(int id)
        {
            var changeGroup = await _issueHistoryService.GetChangeGroupByIdAsync(id);
            if (changeGroup == null)
                return NotFound();

            return Ok(changeGroup);
        }

        [HttpGet("issues/{issueId}/change-groups")]
        public async Task<ActionResult<IEnumerable<ChangeGroupDto>>> GetChangeGroupsByIssue(int issueId)
        {
            var changeGroups = await _issueHistoryService.GetChangeGroupsByIssueIdAsync(issueId);
            return Ok(changeGroups);
        }

        [HttpGet("issues/{issueId}/change-groups/paged")]
        public async Task<ActionResult<ChangeGroupPagedResultDto>> GetChangeGroupsPagedByIssue(
            int issueId, 
            [FromQuery] int pageNumber = 1, 
            [FromQuery] int pageSize = 10)
        {
            var result = await _issueHistoryService.GetChangeGroupsPagedAsync(issueId, pageNumber, pageSize);
            return Ok(result);
        }

        [HttpPut("change-groups/{id}")]
        public async Task<ActionResult<ChangeGroupDto>> UpdateChangeGroup(int id, [FromBody] UpdateChangeGroupDto updateChangeGroupDto)
        {
            try
            {
                var changeGroup = await _issueHistoryService.UpdateChangeGroupAsync(id, updateChangeGroupDto);
                return Ok(changeGroup);
            }
            catch (ArgumentException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpDelete("change-groups/{id}")]
        public async Task<ActionResult> DeleteChangeGroup(int id)
        {
            var result = await _issueHistoryService.DeleteChangeGroupAsync(id);
            if (!result)
                return NotFound();

            return NoContent();
        }

        #endregion

        #region Change Items

        [HttpPost("change-groups/{groupId}/change-items")]
        public async Task<ActionResult<ChangeItemDto>> CreateChangeItem(int groupId, [FromBody] CreateChangeItemDto createChangeItemDto)
        {
            try
            {
                var changeItem = await _issueHistoryService.CreateChangeItemAsync(groupId, createChangeItemDto);
                return CreatedAtAction(nameof(GetChangeItem), new { id = changeItem.Id }, changeItem);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("change-items/{id}")]
        public async Task<ActionResult<ChangeItemDto>> GetChangeItem(int id)
        {
            var changeItem = await _issueHistoryService.GetChangeItemByIdAsync(id);
            if (changeItem == null)
                return NotFound();

            return Ok(changeItem);
        }

        [HttpGet("change-groups/{groupId}/change-items")]
        public async Task<ActionResult<IEnumerable<ChangeItemDto>>> GetChangeItemsByGroup(int groupId)
        {
            var changeItems = await _issueHistoryService.GetChangeItemsByGroupIdAsync(groupId);
            return Ok(changeItems);
        }

        [HttpPut("change-items/{id}")]
        public async Task<ActionResult<ChangeItemDto>> UpdateChangeItem(int id, [FromBody] UpdateChangeItemDto updateChangeItemDto)
        {
            try
            {
                var changeItem = await _issueHistoryService.UpdateChangeItemAsync(id, updateChangeItemDto);
                return Ok(changeItem);
            }
            catch (ArgumentException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpDelete("change-items/{id}")]
        public async Task<ActionResult> DeleteChangeItem(int id)
        {
            var result = await _issueHistoryService.DeleteChangeItemAsync(id);
            if (!result)
                return NotFound();

            return NoContent();
        }

        #endregion

        #region Field History

        [HttpGet("issues/{issueId}/field-history")]
        public async Task<ActionResult<IEnumerable<FieldHistoryDto>>> GetFieldHistory(int issueId)
        {
            var fieldHistory = await _issueHistoryService.GetFieldHistoryAsync(issueId);
            return Ok(fieldHistory);
        }

        [HttpGet("issues/{issueId}/field-history/{field}")]
        public async Task<ActionResult<FieldHistoryDto>> GetFieldHistoryByField(int issueId, string field)
        {
            var fieldHistory = await _issueHistoryService.GetFieldHistoryByFieldAsync(issueId, field);
            if (fieldHistory == null)
                return NotFound();

            return Ok(fieldHistory);
        }

        #endregion

        #region Statistics

        [HttpGet("issues/{issueId}/comments/count")]
        public async Task<ActionResult<int>> GetTotalCommentsCount(int issueId)
        {
            var count = await _issueHistoryService.GetTotalCommentsCountAsync(issueId);
            return Ok(count);
        }

        [HttpGet("issues/{issueId}/change-groups/count")]
        public async Task<ActionResult<int>> GetTotalChangeGroupsCount(int issueId)
        {
            var count = await _issueHistoryService.GetTotalChangeGroupsCountAsync(issueId);
            return Ok(count);
        }

        [HttpGet("issues/{issueId}/recent-changes")]
        public async Task<ActionResult<IEnumerable<ChangeGroupDto>>> GetRecentChanges(int issueId, [FromQuery] int count = 10)
        {
            var recentChanges = await _issueHistoryService.GetRecentChangesAsync(issueId, count);
            return Ok(recentChanges);
        }

        #endregion

        #region System Operations

        [HttpPost("issues/{issueId}/track-changes")]
        public async Task<ActionResult<ChangeGroupDto>> TrackIssueChanges(
            int issueId, 
            [FromBody] Dictionary<string, object> changes,
            [FromQuery] string? description = null)
        {
            try
            {
                // Convert the dictionary to the expected format
                var changeDict = changes.ToDictionary(
                    kvp => kvp.Key,
                    kvp => (oldValue: (object?)null, newValue: kvp.Value, displayName: (string?)null)
                );

                var changeGroup = await _issueHistoryService.TrackIssueChangesAsync(issueId, changeDict, description);
                return Ok(changeGroup);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpPost("issues/{issueId}/system-comment")]
        public async Task<ActionResult<CommentDto>> AddSystemComment(
            int issueId, 
            [FromBody] string message,
            [FromQuery] int? changeGroupId = null)
        {
            try
            {
                var comment = await _issueHistoryService.AddSystemCommentAsync(issueId, message, changeGroupId);
                return Ok(comment);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        #endregion
    }
}
