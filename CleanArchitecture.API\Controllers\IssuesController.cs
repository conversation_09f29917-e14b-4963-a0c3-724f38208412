using CleanArchitectureAPI.Application.Interfaces;  
using CleanArchitectureAPI.Application.DTOs.Issue.Responses;
using CleanArchitectureAPI.Application.DTOs.Common;
using Microsoft.AspNetCore.Mvc;
using CleanArchitectureAPI.Application.Requests.IssueRequests;
using CleanArchitecture.API.Extension;
using Microsoft.AspNetCore.Authorization;

namespace CleanArchitecture.API.Controllers
{
    [Authorize]
	[Route("api/[controller]")]
	[ApiController]
	public class IssuesController : ControllerBase
    {
        private readonly IIssueService _issueService;

        public IssuesController(IIssueService issueService)
        {
            _issueService = issueService;
        }

        [HttpGet]
        public async Task<IActionResult> GetAll()
        {
            var result = await _issueService.GetAllAsync();
            return result.ToActionResult();
        }

        [HttpGet("{projectIssueKey}")]
        public async Task<IActionResult> GetByProjectIssueKey(string projectIssueKey)
        {
            var result = await _issueService.GetByProjectIssueKeyAsync(projectIssueKey);
            return result.ToActionResult();
        }

        [HttpGet("project/{projectKey}")]
        public async Task<IActionResult> GetAllByProjectKey(string projectKey)
        {
            var result = await _issueService.GetAllByProjectKeyAsync(projectKey);
            return result.ToActionResult();
        }

        [HttpGet("assignee/{assigneeId}")]
        public async Task<IActionResult> GetAllByAssignee(int assigneeId)
        {
            var result = await _issueService.GetAllByAssigneeAsync(assigneeId);
            return result.ToActionResult();
        }

        [HttpGet("reporter/{reporterId}")]
        public async Task<IActionResult> GetAllByReporter(int reporterId)
        {
            var result = await _issueService.GetAllByReporterAsync(reporterId);
            return result.ToActionResult();
        }

        [HttpGet("status/{statusId}")]
        public async Task<IActionResult> GetAllByStatus(int statusId)
        {
            var result = await _issueService.GetAllByStatusAsync(statusId);
            return result.ToActionResult();
        }

        [HttpGet("issuetype/{issueTypeId}")]
        public async Task<IActionResult> GetAllByIssueType(int issueTypeId)
        {
            var result = await _issueService.GetAllByIssueTypeAsync(issueTypeId);
            return result.ToActionResult();
        }

        [HttpPost("search")]
        public async Task<IActionResult> GetIssuesByParams([FromBody] GetIssuesByParamsRequest request)
        {
            var result = await _issueService.GetIssuesByParamsAsync(request);
            return result.ToActionResult();
        }

        [HttpPost]
        public async Task<IActionResult> Create(CreateIssueRequest request)
        {
            var result = await _issueService.CreateAsync(request);
            if (result.IsSuccess)
            {
                return CreatedAtAction(nameof(GetByProjectIssueKey),
                    new { projectIssueKey = result.Data.ProjectIssueKey }, result.Data);
            }
            return result.ToActionResult();
        }

        [HttpPut("{projectIssueKey}")]
        public async Task<IActionResult> Update(string projectIssueKey, [FromBody] UpdateIssueRequest request)
        {
            var result = await _issueService.UpdateAsync(projectIssueKey, request);
            return result.ToActionResult();
        }

        [HttpDelete("{projectIssueKey}")]
        public async Task<IActionResult> Delete(string projectIssueKey)
        {
            var result = await _issueService.DeleteAsync(projectIssueKey);
            return result.ToActionResult();
        }

        [HttpPost("{projectIssueKey}/soft-delete")]
        public async Task<IActionResult> SoftDelete(string projectIssueKey)
        {
            var result = await _issueService.SoftDeleteAsync(projectIssueKey);
            return result.ToActionResult();
        }

        [HttpPost("{projectIssueKey}/restore")]
        public async Task<IActionResult> Restore(string projectIssueKey)
        {
            var result = await _issueService.RestoreAsync(projectIssueKey);
            return result.ToActionResult();
        }

        [HttpPost("{projectIssueKey}/assign")]
        public async Task<IActionResult> AssignToUser(string projectIssueKey, [FromBody] AssignIssueRequest request)
        {
            var result = await _issueService.AssignToUserAsync(projectIssueKey, request.AssigneeId);
            return result.ToActionResult();
        }

        [HttpPost("{projectIssueKey}/unassign")]
        public async Task<IActionResult> Unassign(string projectIssueKey)
        {
            var result = await _issueService.UnassignAsync(projectIssueKey);
            return result.ToActionResult();
        }

        [HttpPost("{projectIssueKey}/change-status")]
        public async Task<IActionResult> ChangeStatus(string projectIssueKey, [FromBody] ChangeIssueStatusRequest request)
        {
            var result = await _issueService.ChangeStatusAsync(projectIssueKey, request.StatusId);
            return result.ToActionResult();
        }

        //[HttpPost("{projectIssueKey}/change-resolution")]
        //public async Task<IActionResult> ChangeResolution(string projectIssueKey, [FromBody] ChangeIssueResolutionRequest request)
        //{
        //    var result = await _issueService.ChangeResolutionAsync(projectIssueKey, request.ResolutionId);
        //    return result.ToActionResult();
        //}
    }
}