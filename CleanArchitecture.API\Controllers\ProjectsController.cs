﻿
using CleanArchitecture.API.Extension;
using CleanArchitecture.API.Requests.Project;
using CleanArchitecture.Shared.Helpers;
using CleanArchitectureAPI.Application;
using CleanArchitectureAPI.Application.DTOs.Project.Requests;
using CleanArchitectureAPI.Application.DTOs.Project.Responses;
using CleanArchitectureAPI.Application.Interfaces;
using CleanArchitectureAPI.Application.Services;
using CleanArchitectureAPI.Domain.Entities;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace CleanArchitecture.API.Controllers
{
	[Route("api/[controller]")]
	[Authorize]
	[ApiController]
	public class ProjectsController : ControllerBase
	{
		private readonly IProjectService _projectService;

		public ProjectsController(IProjectService projectService)
		{
			_projectService = projectService;
		}

		[HttpGet("my-projects")]
		public async Task<IActionResult> GetAllProjectsByCurrentUser()
		{
			// Lấy userId từ claim (cookie/token)
			var userIdClaim = User.Claims.FirstOrDefault(c => c.Type == "id" || c.Type.EndsWith("nameidentifier"));
			if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out int userId))
			{
				return Unauthorized("Cannot determine user id from token.");
			}

			var result = await _projectService.GetAllProjectByUserIdAsync(userId);
			return result.ToActionResult();
		}

		[HttpGet("{id}")]
		public async Task<IActionResult> GetProjectById(int id)
		{
			var result = await _projectService.GetByIdAsync(id);
			return result.ToActionResult();
		}

		[HttpGet("{projectId}/members")]
		public async Task<IActionResult> GetProjectMembers(int projectId)
		{
			var result = await _projectService.GetProjectMembersAsync(projectId);
			return result.ToActionResult();
		}

		[HttpPost("add-user")]
		public async Task<IActionResult> AddUserToProject([FromBody] AddUserToProjectRequest request)
		{
			var result = await _projectService.AddUserToProjectAsync(request.UserId, request.ProjectId, request.RoleId);
			return result.ToActionResult();
		}

		[HttpDelete("remove-user")]
		public async Task<IActionResult> RemoveUserFromProject([FromQuery] int userId, [FromQuery] int projectId)
		{
			var result = await _projectService.RemoveUserFromProjectAsync(userId, projectId);
			return result.ToActionResult();
		}

		[HttpPut("update-user-role")]
		public async Task<IActionResult> UpdateUserRoleInProject([FromBody] UpdateUserProjectRoleRequest request)
		{
			var result = await _projectService.UpdateUserRoleInProjectAsync(request.UserId, request.ProjectId, request.RoleId);
			return result.ToActionResult();
		}

		[HttpPost]
		public async Task<IActionResult> CreateProject(CreateProjectRequest request)
		{
			var result = await _projectService.CreateAsync(request);

			if (!result.IsSuccess)
				return result.ToActionResult();

			return CreatedAtAction(nameof(GetProjectById), new { id = result.Data!.Id }, result.Data);
		}

		[HttpPut("{id}")]
		public async Task<IActionResult> UpdateProject(int id, [FromBody] UpdateProjectRequest request)
		{	
		

			return Ok();
		}

		[HttpDelete("{id}")]
		public async Task<IActionResult> SoftDeleteProject(int id)
		{
			return NoContent();
		}

        [HttpGet("check-key/{projectKey}")]
        public async Task<IActionResult> CheckProjectKeyDuplicate(string projectKey)
        {
            var result = await _projectService.CheckProjectKeyDuplicate(projectKey);
            return result.ToActionResult(); // Mã hợp lệ, không trùng
        }

    }
}
