﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using CleanArchitectureAPI.Domain.Entities;
using CleanArchitectureAPI.Infrastructure.Data;
using CleanArchitecture.API.Requests.Status;
using CleanArchitectureAPI.Application.Interfaces;
using CleanArchitectureAPI.Application.DTOs.Status.Responses;
using Microsoft.AspNetCore.Authorization;
using CleanArchitecture.API.Extension;
using CleanArchitectureAPI.Application.Requests.StatusRequests;

namespace CleanArchitecture.API.Controllers
{
	[ApiController]
	[Route("api/[controller]")]
	[Authorize]
	public class StatusController : ControllerBase
	{
		private readonly IStatusService _statusService;

		public StatusController(IStatusService statusService)
		{
			_statusService = statusService;
		}

		[HttpGet("{id}")]
		public async Task<IActionResult> GetById(int id)
		{
			var status = await _statusService.GetByIdAsync(id);
			return status.ToActionResult();
		}

		// For business operations - only active statuses (for dropdowns, creating issues, etc.)
		[HttpGet("project/{projectKey}/active")]
        public async Task<IActionResult> GetAllActiveByProjectKeyAsync(string projectKey)
        {
            var result = await _statusService.GetAllActiveByProjectKeyAsync(projectKey);
			return result.ToActionResult();
        }

		// For admin/history - include inactive statuses (for admin management, reports, etc.)
		[HttpGet("project/{projectKey}")]
        public async Task<IActionResult> GetAllByProjectKeyAsync(string projectKey)
        {
            var result = await _statusService.GetAllByProjectKeyAsync(projectKey);
			return result.ToActionResult();
        }

        [HttpPost]
		public async Task<IActionResult> Create([FromBody] CreateStatusRequest request)
		{
			var result = await _statusService.CreateAsync(request);

			if (!result.IsSuccess)
			{
				return result.ToActionResult();
			}
            return result.ToActionResult();
        }

		[HttpPut("{id}")]
		public async Task<IActionResult> Update(int id, [FromBody] UpdateStatusRequest request)
		{
			var result = await _statusService.UpdateAsync(id, request);
			return result.ToActionResult();

		}

		[HttpDelete("{id}")]
		public async Task<IActionResult> Delete(int id)
		{
			var result = await _statusService.SoftDeleteAsync(id);
			return result.ToActionResult();
		}

		[HttpPost("reorder")]
		public async Task<IActionResult> Reorder([FromBody] ReorderStatusRequest request)
		{
			var result = await _statusService.ReorderAsync(request);
			return result.ToActionResult();
		}
	}
}
