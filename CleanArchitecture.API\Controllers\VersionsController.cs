﻿using CleanArchitecture.API.Extension;
using CleanArchitecture.API.Requests.Version;
using CleanArchitectureAPI.Application.Interfaces;
using CleanArchitectureAPI.Application.DTOs.Version.Responses;
using CleanArchitectureAPI.Application.Requests.VersionRequests;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace CleanArchitecture.API.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class VersionsController : ControllerBase
    {
        private readonly IVersionService _versionService;

        public VersionsController(IVersionService versionService)
        {
            _versionService = versionService;
        }

        // For business operations - only active versions (for dropdowns, creating issues, etc.)
        [HttpGet("project/{projectKey}/active")]
        public async Task<IActionResult> GetAllActiveVersionsByProjectKey([FromRoute] string projectKey)
        {
            var result = await _versionService.GetAllActiveByProjectKeyAsync(projectKey);
            return result.ToActionResult();
        }

        // For admin/history - include inactive versions (for admin management, reports, etc.)
        [HttpGet("project/{projectKey}")]
        public async Task<IActionResult> GetAllVersionsByProjectKey([FromRoute] string projectKey)
        {
            var result = await _versionService.GetAllByProjectKeyAsync(projectKey);
            return result.ToActionResult();
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetVersionById(int id)
        {
            var result = await _versionService.GetByIdAsync(id);
            return result.ToActionResult();
        }

        [HttpPost]
        public async Task<IActionResult> CreateVersion([FromBody] CreateVersionRequest request)
        {
            var result = await _versionService.CreateAsync(request);

            if (!result.IsSuccess)
            {
                return result.ToActionResult();
            }
            return result.ToActionResult();
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateVersion(int id, [FromBody] UpdateVersionRequest request)
        {
            var result = await _versionService.UpdateAsync(id, request);
            return result.ToActionResult();
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> SoftDeleteVersion(int id)
        {
            var result = await _versionService.SoftDeleteAsync(id);
            return result.ToActionResult();
        }

        [HttpPatch("reorder")]
        public async Task<IActionResult> ReorderVersion([FromBody] ReorderVersionRequest request)
        {
            var result = await _versionService.ReorderAsync(request);
            return result.ToActionResult();
        }
    }
}