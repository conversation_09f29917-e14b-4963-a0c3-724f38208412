using CleanArchitectureAPI.Application.DTOs;
using CleanArchitectureAPI.Application.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace CleanArchitecture.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class WorklogController : ControllerBase
    {
        private readonly IWorklogService _worklogService;
        private readonly ICurrentUserService _currentUserService;

        public WorklogController(IWorklogService worklogService, ICurrentUserService currentUserService)
        {
            _worklogService = worklogService;
            _currentUserService = currentUserService;
        }

        #region Basic CRUD Operations

        [HttpPost]
        public async Task<ActionResult<WorklogDto>> CreateWorklog([FromBody] CreateWorklogDto createWorklogDto)
        {
            try
            {
                var isValid = await _worklogService.IsWorklogValidAsync(createWorklogDto);
                if (!isValid)
                    return BadRequest("Invalid worklog data");

                var worklog = await _worklogService.CreateWorklogAsync(createWorklogDto);
                return CreatedAtAction(nameof(GetWorklog), new { id = worklog.Id }, worklog);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<WorklogDto>> GetWorklog(int id)
        {
            var worklog = await _worklogService.GetWorklogByIdAsync(id);
            if (worklog == null)
                return NotFound();

            return Ok(worklog);
        }

        [HttpGet("issues/{issueId}")]
        public async Task<ActionResult<IEnumerable<WorklogDto>>> GetWorklogsByIssue(int issueId)
        {
            var worklogs = await _worklogService.GetWorklogsByIssueIdAsync(issueId);
            return Ok(worklogs);
        }

        [HttpGet("issues/{issueId}/paged")]
        public async Task<ActionResult<WorklogPagedResultDto>> GetWorklogsPagedByIssue(
            int issueId, 
            [FromQuery] int pageNumber = 1, 
            [FromQuery] int pageSize = 10)
        {
            var result = await _worklogService.GetWorklogsPagedAsync(issueId, pageNumber, pageSize);
            return Ok(result);
        }

        [HttpGet("authors/{authorId}")]
        public async Task<ActionResult<IEnumerable<WorklogDto>>> GetWorklogsByAuthor(int authorId)
        {
            var worklogs = await _worklogService.GetWorklogsByAuthorIdAsync(authorId);
            return Ok(worklogs);
        }

        [HttpPut("{id}")]
        public async Task<ActionResult<WorklogDto>> UpdateWorklog(int id, [FromBody] UpdateWorklogDto updateWorklogDto)
        {
            try
            {
                var currentUserId = _currentUserService.GetUserId();
                var canEdit = await _worklogService.CanUserEditWorklogAsync(id, currentUserId);
                if (!canEdit)
                    return Forbid("You can only edit your own worklogs");

                var isValid = await _worklogService.IsWorklogValidAsync(updateWorklogDto);
                if (!isValid)
                    return BadRequest("Invalid worklog data");

                var worklog = await _worklogService.UpdateWorklogAsync(id, updateWorklogDto);
                return Ok(worklog);
            }
            catch (ArgumentException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpDelete("{id}")]
        public async Task<ActionResult> DeleteWorklog(int id)
        {
            try
            {
                var currentUserId = _currentUserService.GetUserId();
                var canDelete = await _worklogService.CanUserDeleteWorklogAsync(id, currentUserId);
                if (!canDelete)
                    return Forbid("You can only delete your own worklogs");

                var result = await _worklogService.DeleteWorklogAsync(id);
                if (!result)
                    return NotFound();

                return NoContent();
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        #endregion

        #region Time Tracking

        [HttpGet("issues/{issueId}/summary")]
        public async Task<ActionResult<WorklogSummaryDto>> GetWorklogSummary(int issueId)
        {
            var summary = await _worklogService.GetWorklogSummaryAsync(issueId);
            return Ok(summary);
        }

        [HttpGet("issues/{issueId}/total-time")]
        public async Task<ActionResult<int>> GetTotalTimeWorked(int issueId)
        {
            var totalTime = await _worklogService.GetTotalTimeWorkedAsync(issueId);
            return Ok(totalTime);
        }

        [HttpGet("issues/{issueId}/authors/{authorId}/total-time")]
        public async Task<ActionResult<int>> GetTotalTimeWorkedByAuthor(int issueId, int authorId)
        {
            var totalTime = await _worklogService.GetTotalTimeWorkedByAuthorAsync(issueId, authorId);
            return Ok(totalTime);
        }

        [HttpPost("issues/time-worked")]
        public async Task<ActionResult<Dictionary<int, int>>> GetTimeWorkedByIssues([FromBody] IEnumerable<int> issueIds)
        {
            var timeWorked = await _worklogService.GetTimeWorkedByIssuesAsync(issueIds);
            return Ok(timeWorked);
        }

        #endregion

        #region Date Range Queries

        [HttpGet("issues/{issueId}/date-range")]
        public async Task<ActionResult<IEnumerable<WorklogDto>>> GetWorklogsByDateRange(
            int issueId, 
            [FromQuery] DateTime fromDate, 
            [FromQuery] DateTime toDate)
        {
            var worklogs = await _worklogService.GetWorklogsByDateRangeAsync(issueId, fromDate, toDate);
            return Ok(worklogs);
        }

        [HttpGet("authors/{authorId}/date-range")]
        public async Task<ActionResult<IEnumerable<WorklogDto>>> GetWorklogsByAuthorAndDateRange(
            int authorId, 
            [FromQuery] DateTime fromDate, 
            [FromQuery] DateTime toDate)
        {
            var worklogs = await _worklogService.GetWorklogsByAuthorAndDateRangeAsync(authorId, fromDate, toDate);
            return Ok(worklogs);
        }

        #endregion

        #region Statistics and Reporting

        [HttpGet("issues/{issueId}/recent")]
        public async Task<ActionResult<IEnumerable<WorklogDto>>> GetRecentWorklogs(int issueId, [FromQuery] int count = 10)
        {
            var recentWorklogs = await _worklogService.GetRecentWorklogsAsync(issueId, count);
            return Ok(recentWorklogs);
        }

        [HttpGet("issues/{issueId}/statistics")]
        public async Task<ActionResult<Dictionary<string, object>>> GetWorklogStatistics(int issueId)
        {
            var statistics = await _worklogService.GetWorklogStatisticsAsync(issueId);
            return Ok(statistics);
        }

        [HttpGet("authors/{authorId}/statistics")]
        public async Task<ActionResult<Dictionary<string, object>>> GetAuthorWorklogStatistics(
            int authorId, 
            [FromQuery] DateTime? fromDate = null, 
            [FromQuery] DateTime? toDate = null)
        {
            var statistics = await _worklogService.GetAuthorWorklogStatisticsAsync(authorId, fromDate, toDate);
            return Ok(statistics);
        }

        #endregion

        #region My Worklogs

        [HttpGet("my-worklogs")]
        public async Task<ActionResult<IEnumerable<WorklogDto>>> GetMyWorklogs()
        {
            var currentUserId = _currentUserService.GetUserId();
            var worklogs = await _worklogService.GetWorklogsByAuthorIdAsync(currentUserId);
            return Ok(worklogs);
        }

        [HttpGet("my-worklogs/date-range")]
        public async Task<ActionResult<IEnumerable<WorklogDto>>> GetMyWorklogsByDateRange(
            [FromQuery] DateTime fromDate, 
            [FromQuery] DateTime toDate)
        {
            var currentUserId = _currentUserService.GetUserId();
            var worklogs = await _worklogService.GetWorklogsByAuthorAndDateRangeAsync(currentUserId, fromDate, toDate);
            return Ok(worklogs);
        }

        [HttpGet("my-worklogs/statistics")]
        public async Task<ActionResult<Dictionary<string, object>>> GetMyWorklogStatistics(
            [FromQuery] DateTime? fromDate = null, 
            [FromQuery] DateTime? toDate = null)
        {
            var currentUserId = _currentUserService.GetUserId();
            var statistics = await _worklogService.GetAuthorWorklogStatisticsAsync(currentUserId, fromDate, toDate);
            return Ok(statistics);
        }

        #endregion
    }
}
