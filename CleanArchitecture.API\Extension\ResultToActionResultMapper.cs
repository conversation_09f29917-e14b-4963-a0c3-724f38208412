﻿using CleanArchitectureAPI.Application.Common;
using Microsoft.AspNetCore.Mvc;

namespace CleanArchitecture.API.Extension
{
    public static class ResultToActionResultMapper
    {
        public static IActionResult ToActionResult<T>(this Result<T> result)
        {
            if (result.IsSuccess)
                return new OkObjectResult(result.Data);

            return result.ErrorCode switch
            {
                ErrorCode.NotFound or ErrorCode.ProjectNotFound =>
                    new NotFoundObjectResult( new { message = result.ErrorMessage }),

                ErrorCode.ValidationError =>
                    new BadRequestObjectResult(new { message = result.ErrorMessage }),

                ErrorCode.Conflict or ErrorCode.CategoryNameDuplicate =>
                    new ConflictObjectResult(new { message = result.ErrorMessage }),

                ErrorCode.Unauthorized or ErrorCode.InvalidCredentials or ErrorCode.InvalidToken =>
                    new UnauthorizedObjectResult(new { message = result.ErrorMessage }),

                ErrorCode.Forbidden or ErrorCode.PermissionDenied =>
                    new ObjectResult(new { message = result.ErrorMessage }) { StatusCode = 403 },

                _ => new ObjectResult(new { message = result.ErrorMessage ?? "Lỗi không xác định" }) { StatusCode = 500 }
            };

        }

        public static IActionResult ToActionResult(this Result result) {

            if (result.IsSuccess)
                return new OkObjectResult(result.IsSuccess);


            return result.ErrorCode switch
            {
                ErrorCode.NotFound or ErrorCode.ProjectNotFound =>
                    new NotFoundObjectResult(new { message = result.ErrorMessage }),

                ErrorCode.ValidationError =>
                    new BadRequestObjectResult(new { message = result.ErrorMessage }),

                ErrorCode.Conflict or ErrorCode.CategoryNameDuplicate =>
                    new ConflictObjectResult(new { message = result.ErrorMessage }),

                ErrorCode.Unauthorized or ErrorCode.InvalidCredentials or ErrorCode.InvalidToken =>
                    new UnauthorizedObjectResult(new { message = result.ErrorMessage }),

                ErrorCode.Forbidden or ErrorCode.PermissionDenied =>
                    new ObjectResult(new { message = result.ErrorMessage }) { StatusCode = 403 },

                _ => new ObjectResult(new { message = result.ErrorMessage ?? "Lỗi không xác định" }) { StatusCode = 500 }
            };

        }







    }
}
