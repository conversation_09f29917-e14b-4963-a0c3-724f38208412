﻿using System.Net;
using System.Text.Json;
using CleanArchitectureAPI.Domain.Exceptions;

namespace CleanArchitecture.API.Middlewares
{
	public class ExceptionHandlingMiddleware
	{
		private readonly RequestDelegate _next;
		private readonly ILogger<ExceptionHandlingMiddleware> _logger;


		public ExceptionHandlingMiddleware(RequestDelegate next, ILogger<ExceptionHandlingMiddleware> logger)
		{
			_next = next;
			_logger = logger;
		}


		public async Task Invoke(HttpContext context)
		{
			try
			{
				await _next(context);
			}
			catch (DomainException ex)
			{
				_logger.LogWarning(ex, "Lỗi nghiệp vụ xảy ra: {Message}", ex.Message);

				context.Response.ContentType = "application/json";
				context.Response.StatusCode = GetStatusCode(ex);

				var result = JsonSerializer.Serialize(new
				{
					success = false,
					code = ex.ErrorCode,
					message = ex.Message
				});

				await context.Response.WriteAsync(result);
			}
			catch (Exception ex)
			{
				_logger.LogError(ex, "Lỗi hệ thống không xác đ<PERSON>");

				context.Response.ContentType = "application/json";
				context.Response.StatusCode = (int)HttpStatusCode.InternalServerError;

				var result = JsonSerializer.Serialize(new
				{
					success = false,
					code = "INTERNAL_ERROR",
					message = "Đã xảy ra lỗi hệ thống. Vui lòng thử lại sau."
				});

				await context.Response.WriteAsync(result);
			}

		}

		private int GetStatusCode(DomainException ex) =>
	   ex switch
	   {
		   NotFoundException => StatusCodes.Status404NotFound,
		   ConflictException => StatusCodes.Status409Conflict,
		   ForbiddenException => StatusCodes.Status403Forbidden,
		   BusinessRuleException => StatusCodes.Status400BadRequest,
		   _ => StatusCodes.Status400BadRequest
	   };
	}
}
