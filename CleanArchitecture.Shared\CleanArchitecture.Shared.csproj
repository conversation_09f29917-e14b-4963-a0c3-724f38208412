﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
    <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.3.0" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="6.35.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\CleanArchitectureAPI.Domain\CleanArchitectureAPI.Domain.csproj" />
  </ItemGroup>

</Project>
