﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;

namespace CleanArchitecture.Shared.Helpers
{
	public class CookieHelper
	{
		public static void SetAccessToken(HttpResponse response, string token)
		{
			response.Cookies.Append("accessToken", token, new CookieOptions
			{
				HttpOnly = true,
				Secure = true,
				SameSite = SameSiteMode.None,
				Expires = DateTime.UtcNow.AddHours(1)
			});
		}

		public static void ClearAccessToken(HttpResponse response)
		{
			response.Cookies.Delete("accessToken", new CookieOptions
			{
				HttpOnly = true,
				Secure = true,
				SameSite = SameSiteMode.None
			});
		}
	}
}
