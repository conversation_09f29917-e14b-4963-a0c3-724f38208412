﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using BCrypt.Net;

namespace CleanArchitecture.Shared.Helpers
{
    public class PasswordHelper
    {
        public static string HassPassword (string password)
        {
            return BCrypt.Net.BCrypt.HashPassword(password);
        }

        public static bool VerifyPassword (string enteredPassword, string hasedPassword)
        {
            return BCrypt.Net.BCrypt.Verify(enteredPassword, hasedPassword);
        }
    }
}
