﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CleanArchitectureAPI.Application.Common
{
	public class Result<T>
	{
		public bool IsSuccess { get; init; }

		public T? Data;

		public ErrorCode? ErrorCode { get; init; }

		public string? ErrorMessage { get; init; }

		public Result() { }

		public static Result<T> Success(T data) => new Result<T>()
		{
			IsSuccess = true,
			Data = data
		};

		public static Result<T> Failure(ErrorCode errorCode, string errorMessage) => new Result<T>
		{
			IsSuccess = false,
			ErrorCode = errorCode,
			ErrorMessage = errorMessage
		};

        public static Result<T> Failure(string errorMessage) => new Result<T>
        {
            IsSuccess = false,
            ErrorMessage = errorMessage
        };
    }

	public class Result
	{
		public bool IsSuccess { get; init; }
		public ErrorCode? ErrorCode { get; init; }
		public string? ErrorMessage { get; init; }

		private Result() { }

		public static Result Success() => new()
		{
			IsSuccess = true
		};

		public static Result Failure(ErrorCode errorCode, string errorMessage) => new()
		{
			IsSuccess = false,
			ErrorCode = errorCode,
			ErrorMessage = errorMessage
		};
	}


	public enum ErrorCode
	{
		NotFound,
		ValidationError,
		BadRequest,
		Conflict,
		Unauthorized,
		Forbidden,
		Unexpected,
		CategoryNameDuplicate,
		UserAlreadyExists,
		InvalidCredentials,
		InvalidToken,
		PermissionDenied,
		ProjectNotFound,
	}
}
