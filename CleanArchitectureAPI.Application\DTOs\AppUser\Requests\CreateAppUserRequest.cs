using CleanArchitectureAPI.Domain.Enums;

namespace CleanArchitectureAPI.Application.DTOs.AppUser.Requests
{
    public class CreateAppUserRequest
    {
        public string FullName { get; set; } = string.Empty;
        public required string Email { get; set; }
        public required string Password { get; set; }
        public string? Phone { get; set; }
        public string? Bio { get; set; }
        public string? Address { get; set; }
        public UserStatus Status { get; set; } = UserStatus.Online;
    }
}


