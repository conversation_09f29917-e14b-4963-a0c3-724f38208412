using CleanArchitectureAPI.Domain.Enums;

namespace CleanArchitectureAPI.Application.DTOs.AppUser.Responses
{
    public class AppUserResponse
    {
        public int Id { get; set; }
        public string FullName { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public UserStatus Status { get; set; }
        public string? Avatar { get; set; }
        public string? Phone { get; set; }
        public string? Bio { get; set; }
        public string? Address { get; set; }
        public DateTime LastActive { get; set; }
        public DateTime LastLoginTime { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? LastModifiedAt { get; set; }
        public bool IsActive { get; set; }
    }
}


