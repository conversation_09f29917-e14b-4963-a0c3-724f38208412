namespace CleanArchitectureAPI.Application.DTOs
{
    public class ChangeGroupDto
    {
        public int Id { get; set; }
        public int IssueId { get; set; }
        public int AuthorId { get; set; }
        public string AuthorName { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string ChangeType { get; set; } = "Manual";
        public DateTime CreatedAt { get; set; }
        public IEnumerable<ChangeItemDto> ChangeItems { get; set; } = new List<ChangeItemDto>();
        public IEnumerable<CommentDto> Comments { get; set; } = new List<CommentDto>();
    }

    public class CreateChangeGroupDto
    {
        public int IssueId { get; set; }
        public string? Description { get; set; }
        public string ChangeType { get; set; } = "Manual";
        public IEnumerable<CreateChangeItemDto> ChangeItems { get; set; } = new List<CreateChangeItemDto>();
    }

    public class UpdateChangeGroupDto
    {
        public string? Description { get; set; }
        public string ChangeType { get; set; } = "Manual";
    }

    public class ChangeGroupPagedResultDto
    {
        public IEnumerable<ChangeGroupDto> ChangeGroups { get; set; } = new List<ChangeGroupDto>();
        public int TotalCount { get; set; }
        public int PageNumber { get; set; }
        public int PageSize { get; set; }
        public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
        public bool HasPreviousPage => PageNumber > 1;
        public bool HasNextPage => PageNumber < TotalPages;
    }
}
