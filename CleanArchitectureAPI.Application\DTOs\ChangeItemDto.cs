namespace CleanArchitectureAPI.Application.DTOs
{
    public class ChangeItemDto
    {
        public int Id { get; set; }
        public int GroupId { get; set; }
        public string FieldType { get; set; } = string.Empty;
        public string Field { get; set; } = string.Empty;
        public string? FieldDisplayName { get; set; }
        public string? OldValue { get; set; }
        public string? OldString { get; set; }
        public string? NewValue { get; set; }
        public string? NewString { get; set; }
        public int DisplayOrder { get; set; }
        public DateTime CreatedAt { get; set; }
    }

    public class CreateChangeItemDto
    {
        public string FieldType { get; set; } = string.Empty;
        public string Field { get; set; } = string.Empty;
        public string? FieldDisplayName { get; set; }
        public string? OldValue { get; set; }
        public string? OldString { get; set; }
        public string? NewValue { get; set; }
        public string? NewString { get; set; }
        public int DisplayOrder { get; set; } = 0;
    }

    public class UpdateChangeItemDto
    {
        public string FieldType { get; set; } = string.Empty;
        public string Field { get; set; } = string.Empty;
        public string? FieldDisplayName { get; set; }
        public string? OldValue { get; set; }
        public string? OldString { get; set; }
        public string? NewValue { get; set; }
        public string? NewString { get; set; }
        public int DisplayOrder { get; set; } = 0;
    }

    public class FieldHistoryDto
    {
        public string Field { get; set; } = string.Empty;
        public string? FieldDisplayName { get; set; }
        public IEnumerable<ChangeItemDto> Changes { get; set; } = new List<ChangeItemDto>();
    }
}
