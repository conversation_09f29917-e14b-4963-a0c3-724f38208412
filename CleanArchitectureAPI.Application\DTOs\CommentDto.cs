namespace CleanArchitectureAPI.Application.DTOs
{
    public class CommentDto
    {
        public int Id { get; set; }
        public int IssueId { get; set; }
        public int AuthorId { get; set; }
        public string AuthorName { get; set; } = string.Empty;
        public string ActionBody { get; set; } = string.Empty;
        public string CommentType { get; set; } = "User";
        public int? ChangeGroupId { get; set; }
        public bool IsEdited { get; set; }
        public DateTime? LastEditedAt { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? LastModifiedAt { get; set; }
    }

    public class CreateCommentDto
    {
        public int IssueId { get; set; }
        public string ActionBody { get; set; } = string.Empty;
        public string CommentType { get; set; } = "User";
        public int? ChangeGroupId { get; set; }
    }

    public class UpdateCommentDto
    {
        public string ActionBody { get; set; } = string.Empty;
    }

    public class CommentPagedResultDto
    {
        public IEnumerable<CommentDto> Comments { get; set; } = new List<CommentDto>();
        public int TotalCount { get; set; }
        public int PageNumber { get; set; }
        public int PageSize { get; set; }
        public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
        public bool HasPreviousPage => PageNumber > 1;
        public bool HasNextPage => PageNumber < TotalPages;
    }
}
