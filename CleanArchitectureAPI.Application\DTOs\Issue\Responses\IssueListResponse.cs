namespace CleanArchitectureAPI.Application.DTOs.Issue.Responses
{
    public class IssueListResponse
    {
        public int Id { get; set; }
        public string ProjectIssueKey { get; set; } = string.Empty;
        public string Subject { get; set; } = string.Empty;
        public string? AssigneeName { get; set; }
        public string ReporterName { get; set; } = string.Empty;
        public DateTime? DueDate { get; set; }
        public string IssueTypeName { get; set; } = string.Empty;
        public string? IssueTypeColor { get; set; }
        public string StatusName { get; set; } = string.Empty;
        public string? StatusColor { get; set; }
        public string PriorityName { get; set; } = string.Empty;
        public string? PriorityColor { get; set; }
        public List<string> Milestones { get; set; } = new List<string>();
        public List<string> Versions { get; set; } = new List<string>();
        public List<string> Categories { get; set; } = new List<string>();
        public DateTime CreatedAt { get; set; }
        public DateTime? LastModifiedAt { get; set; }
    }
}


