using CleanArchitectureAPI.Application.DTOs.Category.Responses;
using CleanArchitectureAPI.Application.DTOs.Milestone.Responses;
using CleanArchitectureAPI.Application.DTOs.Version.Responses;

namespace CleanArchitectureAPI.Application.DTOs.Issue.Responses
{
    public class IssueResponse
    {
        public int Id { get; set; }
        public string ProjectIssueKey { get; set; } = string.Empty;
        public string Subject { get; set; } = string.Empty;
        public string? Description { get; set; }

        // Project info
        public string ProjectKey { get; set; } = string.Empty;
        public int ProjectId { get; set; }

        // User assignments
        public int? AssigneeId { get; set; }
        public string? AssigneeName { get; set; }
        public string? AssigneeEmail { get; set; }

        public int ReporterId { get; set; }
        public string ReporterName { get; set; } = string.Empty;
        public string ReporterEmail { get; set; } = string.Empty;

        // Dates
        public DateTime? DueDate { get; set; }

        // Issue Type
        public int IssueTypeId { get; set; }
        public string IssueTypeName { get; set; } = string.Empty;
        public string? IssueTypeColor { get; set; }

        // Status
        public int StatusId { get; set; }
        public string StatusName { get; set; } = string.Empty;
        public string? StatusColor { get; set; }

        // Priority
        public int PriorityId { get; set; }
        public string PriorityName { get; set; } = string.Empty;
        public string? PriorityColor { get; set; }

        // Categories, Milestones, Versions (collections)
        public List<CategoryResponse> Categories { get; set; } = new List<CategoryResponse>();
        public List<MilestoneResponse> Milestones { get; set; } = new List<MilestoneResponse>();
        public List<VersionResponse> Versions { get; set; } = new List<VersionResponse>();

        // Audit fields
        public bool IsActive { get; set; }
        public bool IsDeleted { get; set; }
        public DateTime CreatedAt { get; set; }
        public int? CreatedById { get; set; }
        public string? CreatedByName { get; set; }
        public DateTime? LastModifiedAt { get; set; }
        public int? LastModifiedById { get; set; }
        public string? LastModifiedByName { get; set; }

        // Comment count
        public int CommentCount { get; set; }
    }
}
