namespace CleanArchitectureAPI.Application.DTOs.Milestone.Responses
{
    public class MilestoneResponse
    {
        public int Id { get; set; }
        public string Name { get; set; } = null!;
        public string? Description { get; set; }
        public string Color { get; set; } = "#E0E0E0";
        public int Order { get; set; }
        public int ProjectId { get; set; }
        public string ProjectKey { get; set; } = null!;
        public DateTime CreatedAt { get; set; }
        public DateTime? LastModifiedAt { get; set; }
        public bool IsActive { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public bool IsReleased { get; set; }
    }
}
