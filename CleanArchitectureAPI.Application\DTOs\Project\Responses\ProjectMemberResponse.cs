namespace CleanArchitectureAPI.Application.DTOs.Project.Responses
{
    public class ProjectMemberResponse
    {
        public int UserId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public string UserEmail { get; set; } = string.Empty;
        public string? RoleName { get; set; }
        public DateTime JoinedAt { get; set; }
        public bool IsActive { get; set; }
    }
}


