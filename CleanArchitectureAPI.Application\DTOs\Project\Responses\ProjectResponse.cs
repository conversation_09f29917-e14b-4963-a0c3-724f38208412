namespace CleanArchitectureAPI.Application.DTOs.Project.Responses
{
    public class ProjectResponse
    {
        public int Id { get; set; }
        public string ProjectKey { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string? AvatarUrl { get; set; }
        public int Last_Issue_Number { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public int? CreatedById { get; set; }
        public DateTime? LastModifiedAt { get; set; }
        public int? LastModifiedById { get; set; }
        
        // Thông tin về role của user hiện tại trong project (nếu có)
        public string? UserRole { get; set; }
        public DateTime? UserJoinedAt { get; set; }
        
        // Số lượng members trong project
        public int MemberCount { get; set; }
    }
}


