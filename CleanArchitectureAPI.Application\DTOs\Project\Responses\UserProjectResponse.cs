namespace CleanArchitectureAPI.Application.DTOs.Project.Responses
{
    public class UserProjectResponse
    {
        public int UserId { get; set; }
        public int ProjectId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public string ProjectName { get; set; } = string.Empty;
        public int? RoleId { get; set; }
        public string? RoleName { get; set; }
        public DateTime JoinedAt { get; set; }
        public bool IsActive { get; set; }
    }
}


