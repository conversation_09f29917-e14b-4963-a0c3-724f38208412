namespace CleanArchitectureAPI.Application.DTOs
{
    public class WorklogDto
    {
        public int Id { get; set; }
        public int IssueId { get; set; }
        public int AuthorId { get; set; }
        public string AuthorName { get; set; } = string.Empty;
        public string? WorklogBody { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public int TimeWorkedSeconds { get; set; }
        public int? RemainingEstimateSeconds { get; set; }
        public bool IsPublic { get; set; }
        public string WorklogType { get; set; } = "Manual";
        public bool IsEdited { get; set; }
        public DateTime? LastEditedAt { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? LastModifiedAt { get; set; }

        // Computed properties for display
        public string TimeWorkedFormatted => FormatTimeSpan(TimeWorkedSeconds);
        public string? RemainingEstimateFormatted => RemainingEstimateSeconds.HasValue ? FormatTimeSpan(RemainingEstimateSeconds.Value) : null;

        private static string FormatTimeSpan(int seconds)
        {
            var timeSpan = TimeSpan.FromSeconds(seconds);
            if (timeSpan.TotalDays >= 1)
                return $"{(int)timeSpan.TotalDays}d {timeSpan.Hours}h {timeSpan.Minutes}m";
            if (timeSpan.TotalHours >= 1)
                return $"{timeSpan.Hours}h {timeSpan.Minutes}m";
            return $"{timeSpan.Minutes}m";
        }
    }

    public class CreateWorklogDto
    {
        public int IssueId { get; set; }
        public string? WorklogBody { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public int TimeWorkedSeconds { get; set; }
        public int? RemainingEstimateSeconds { get; set; }
        public bool IsPublic { get; set; } = true;
        public string WorklogType { get; set; } = "Manual";
    }

    public class UpdateWorklogDto
    {
        public string? WorklogBody { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public int TimeWorkedSeconds { get; set; }
        public int? RemainingEstimateSeconds { get; set; }
        public bool IsPublic { get; set; } = true;
        public string WorklogType { get; set; } = "Manual";
    }

    public class WorklogPagedResultDto
    {
        public IEnumerable<WorklogDto> Worklogs { get; set; } = new List<WorklogDto>();
        public int TotalCount { get; set; }
        public int PageNumber { get; set; }
        public int PageSize { get; set; }
        public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
        public bool HasPreviousPage => PageNumber > 1;
        public bool HasNextPage => PageNumber < TotalPages;
    }

    public class WorklogSummaryDto
    {
        public int IssueId { get; set; }
        public int TotalTimeWorkedSeconds { get; set; }
        public int WorklogCount { get; set; }
        public string TotalTimeWorkedFormatted => FormatTimeSpan(TotalTimeWorkedSeconds);

        private static string FormatTimeSpan(int seconds)
        {
            var timeSpan = TimeSpan.FromSeconds(seconds);
            if (timeSpan.TotalDays >= 1)
                return $"{(int)timeSpan.TotalDays}d {timeSpan.Hours}h {timeSpan.Minutes}m";
            if (timeSpan.TotalHours >= 1)
                return $"{timeSpan.Hours}h {timeSpan.Minutes}m";
            return $"{timeSpan.Minutes}m";
        }
    }
}
