﻿using CleanArchitectureAPI.Application.Common;
using CleanArchitectureAPI.Application.DTOs.AppUser.Requests;
using CleanArchitectureAPI.Application.DTOs.AppUser.Responses;

namespace CleanArchitectureAPI.Application.Interfaces
{
    public interface IAppUserServices
    {
        Task<Result<IEnumerable<AppUserResponse>>> GetAllAsync();
        Task<Result<AppUserResponse>> GetByIdAsync(int id);
        Task<Result<AppUserResponse>> CreateAsync(CreateAppUserRequest request);
        Task<Result<AppUserResponse>> UpdateAsync(int id, UpdateAppUserRequest request);
        Task<Result<bool>> DeleteAsync(int id);
        Task<Result<AppUserResponse>> UpdateProfileAsync(int id, UpdateProfileRequest request);
        Task<Result<bool>> ChangePasswordAsync(int id, ChangePasswordRequest request);
        Task<Result<AppUserResponse>> GetCurrentUserAsync();
        Task<Result<string>> SetHasCompletedInitialSetupAsync(int userId);
        Task<Result<IEnumerable<AppUserResponse>>> GetUsersByProjectKeyAsync(string projectKey);
        Task<Result<IEnumerable<AppUserResponse>>> GetAllActiveByProjectKeyAsync(string projectKey);
	}
}
