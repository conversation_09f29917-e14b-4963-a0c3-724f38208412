﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CleanArchitectureAPI.Application.DTOs.Auth.Responses;
using CleanArchitectureAPI.Application.Common;
using CleanArchitectureAPI.Application.DTOs.Auth.Requests;
using CleanArchitectureAPI.Domain.Entities.User;

namespace CleanArchitectureAPI.Application.Interfaces
{
	public interface IAuthService 
	{
		Task<Result<AuthResponse>> RegisterAsync(RegisterUserRequest registerUserDto);
		Task<Result<AuthResponse>> LoginAsync (string username, string password);
		Task<Result<bool>> ChangePassword  (int id, string oldPassword, string newPassword);
	}
}
