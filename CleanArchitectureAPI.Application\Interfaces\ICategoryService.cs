﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CleanArchitecture.API.Requests.Category;
using CleanArchitectureAPI.Application.Common;
using CleanArchitectureAPI.Application.DTOs.Category.Responses;
using CleanArchitectureAPI.Application.Requests.CategoryRequests;

namespace CleanArchitectureAPI.Application.Interfaces
{
	public interface ICategoryService
	{
		Task<Result<CategoryResponse>> GetByIdAsync(int id);

		// For business operations - only active categories
		Task<Result<IEnumerable<CategoryResponse>>> GetAllActiveByProjectKeyAsync(string projectKey);

		// For admin/history - include inactive categories
		Task<Result<IEnumerable<CategoryResponse>>> GetAllByProjectKeyAsync(string projectKey);

		Task<Result<CategoryResponse>> CreateAsync(CreateCategoryRequest request);
		Task<Result<bool>> UpdateAsync(int id, UpdateCategoryRequest request);
		Task<Result<bool>> SoftDeleteAsync(int id);
		Task<Result<bool>> ReorderAsync(ReorderCategoryRequest request);
	}


}
