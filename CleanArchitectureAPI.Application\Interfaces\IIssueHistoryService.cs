using CleanArchitectureAPI.Application.DTOs;

namespace CleanArchitectureAPI.Application.Interfaces
{
    public interface IIssueHistoryService
    {
        // Comment operations
        Task<CommentDto> CreateCommentAsync(CreateCommentDto createCommentDto);
        Task<CommentDto?> GetCommentByIdAsync(int id);
        Task<IEnumerable<CommentDto>> GetCommentsByIssueIdAsync(int issueId);
        Task<CommentPagedResultDto> GetCommentsPagedAsync(int issueId, int pageNumber, int pageSize);
        Task<CommentDto> UpdateCommentAsync(int id, UpdateCommentDto updateCommentDto);
        Task<bool> DeleteCommentAsync(int id);

        // ChangeGroup operations
        Task<ChangeGroupDto> CreateChangeGroupAsync(CreateChangeGroupDto createChangeGroupDto);
        Task<ChangeGroupDto?> GetChangeGroupByIdAsync(int id);
        Task<IEnumerable<ChangeGroupDto>> GetChangeGroupsByIssueIdAsync(int issueId);
        Task<ChangeGroupPagedResultDto> GetChangeGroupsPagedAsync(int issueId, int pageNumber, int pageSize);
        Task<ChangeGroupDto> UpdateChangeGroupAsync(int id, UpdateChangeGroupDto updateChangeGroupDto);
        Task<bool> DeleteChangeGroupAsync(int id);

        // ChangeItem operations
        Task<ChangeItemDto> CreateChangeItemAsync(int groupId, CreateChangeItemDto createChangeItemDto);
        Task<ChangeItemDto?> GetChangeItemByIdAsync(int id);
        Task<IEnumerable<ChangeItemDto>> GetChangeItemsByGroupIdAsync(int groupId);
        Task<ChangeItemDto> UpdateChangeItemAsync(int id, UpdateChangeItemDto updateChangeItemDto);
        Task<bool> DeleteChangeItemAsync(int id);

        // Field history
        Task<IEnumerable<FieldHistoryDto>> GetFieldHistoryAsync(int issueId);
        Task<FieldHistoryDto?> GetFieldHistoryByFieldAsync(int issueId, string field);

        // Issue change tracking
        Task<ChangeGroupDto> TrackIssueChangesAsync(int issueId, Dictionary<string, (object? oldValue, object? newValue, string? displayName)> changes, string? description = null);
        Task<CommentDto> AddSystemCommentAsync(int issueId, string message, int? changeGroupId = null);

        // Statistics
        Task<int> GetTotalCommentsCountAsync(int issueId);
        Task<int> GetTotalChangeGroupsCountAsync(int issueId);
        Task<IEnumerable<ChangeGroupDto>> GetRecentChangesAsync(int issueId, int count = 10);
    }
}
