using CleanArchitectureAPI.Application.Common;
using CleanArchitectureAPI.Application.DTOs;
using CleanArchitectureAPI.Application.DTOs.Issue.Responses;
using CleanArchitectureAPI.Application.DTOs.Common;
using CleanArchitectureAPI.Application.Requests.IssueRequests;

namespace CleanArchitectureAPI.Application.Interfaces
{
    public interface IIssueService
    {
        // CRUD operations using projectIssueKey
        Task<Result<IssueResponse>> GetByProjectIssueKeyAsync(string projectIssueKey);
        Task<Result<IssueResponse>> CreateAsync(CreateIssueRequest request);
        Task<Result<IssueResponse>> UpdateAsync(string projectIssueKey, UpdateIssueRequest request);
        Task<Result<bool>> DeleteAsync(string projectIssueKey);
        
        // Additional operations
        Task<Result<IEnumerable<IssueResponse>>> GetAllAsync();
        Task<Result<IEnumerable<IssueResponse>>> GetAllByProjectKeyAsync(string projectKey);
        Task<Result<IEnumerable<IssueResponse>>> GetAllByAssigneeAsync(int assigneeId);
        Task<Result<IEnumerable<IssueResponse>>> GetAllByReporterAsync(int reporterId);
        Task<Result<IEnumerable<IssueResponse>>> GetAllByStatusAsync(int statusId);
        Task<Result<IEnumerable<IssueResponse>>> GetAllByIssueTypeAsync(int issueTypeId);
        
        // Soft delete
        Task<Result<bool>> SoftDeleteAsync(string projectIssueKey);
        Task<Result<bool>> RestoreAsync(string projectIssueKey);
        
        // Assignment operations
        Task<Result<bool>> AssignToUserAsync(string projectIssueKey, int assigneeId);
        Task<Result<bool>> UnassignAsync(string projectIssueKey);
        
        // Status operations
        Task<Result<bool>> ChangeStatusAsync(string projectIssueKey, int statusId);
     
        Task<Result<CleanArchitectureAPI.Application.DTOs.Common.PagedResultDto<IssueResponse>>> GetIssuesByParamsAsync(GetIssuesByParamsRequest request);
    }
}
