﻿using CleanArchitecture.API.Requests;
using CleanArchitecture.API.Requests.IssueType;
using CleanArchitectureAPI.Application.Common;
using CleanArchitectureAPI.Application.DTOs.IssueType.Responses;
using CleanArchitectureAPI.Application.Requests.IssueTypeRequests;

namespace CleanArchitectureAPI.Application.Interfaces
{
    public interface IIssueTypeService
    {
        // For business operations - only active issue types
        Task<Result<IEnumerable<IssueTypeResponse>>> GetAllActiveByProjectKeyAsync(string projectKey);

        // For admin/history - include inactive issue types
        Task<Result<IEnumerable<IssueTypeResponse>>> GetAllByProjectKeyAsync(string projectKey);

        Task<Result<IssueTypeResponse>> GetByIdAsync(int id);
        Task<Result<IssueTypeResponse>> CreateAsync(CreateIssueTypeRequest request);
        Task<Result<bool>> UpdateAsync(int id, UpdateIssueTypeRequest request);
        Task<Result<bool>> SoftDeleteAsync(int id);
        Task<Result<bool>> ReorderAsync(ReorderIssueTypeRequest request);
    }
}