﻿using CleanArchitecture.API.Requests.Milestone;
using CleanArchitectureAPI.Application.Common;
using CleanArchitectureAPI.Application.DTOs.Milestone.Responses;
using CleanArchitectureAPI.Application.Requests.MilestoneRequests;

namespace CleanArchitectureAPI.Application.Interfaces
{
    public interface IMilestoneService
    {
        // For business operations - only active milestones
        Task<Result<IEnumerable<MilestoneResponse>>> GetAllActiveByProjectKeyAsync(string projectKey);

        // For admin/history - include inactive milestones
        Task<Result<IEnumerable<MilestoneResponse>>> GetAllByProjectKeyAsync(string projectKey);

        Task<Result<MilestoneResponse>> GetByIdAsync(int id);
        Task<Result<MilestoneResponse>> CreateAsync(CreateMilestoneRequest request);
        Task<Result<bool>> UpdateAsync(int id, UpdateMilestoneRequest request);
        Task<Result<bool>> SoftDeleteAsync(int id);
        Task<Result<bool>> ReorderAsync(ReorderMilestoneRequest request);
    }
}