﻿using CleanArchitecture.API.Requests.Priority;
using CleanArchitectureAPI.Application.Common;
using CleanArchitectureAPI.Application.DTOs.Priority.Responses;
using CleanArchitectureAPI.Application.Requests.PriorityRequests;

namespace CleanArchitectureAPI.Application.Interfaces
{
    public interface IPriorityService
    {
        // For business operations - only active priorities
        Task<Result<IEnumerable<PriorityResponse>>> GetAllActiveByProjectKeyAsync(string projectKey);

        // For admin/history - include inactive priorities
        Task<Result<IEnumerable<PriorityResponse>>> GetAllByProjectKeyAsync(string projectKey);

        Task<Result<PriorityResponse>> GetByIdAsync(int id);
        Task<Result<PriorityResponse>> CreateAsync(CreatePriorityRequest request);
        Task<Result<bool>> UpdateAsync(int id, UpdatePriorityRequest request);
        Task<Result<bool>> SoftDeleteAsync(int id);
        Task<Result<bool>> ReorderAsync(ReorderPriorityRequest request);
    }
}