﻿
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CleanArchitecture.API.Requests.Category;
using CleanArchitecture.API.Requests.Project;
using CleanArchitectureAPI.Application.Common;
using CleanArchitectureAPI.Application.DTOs;
using CleanArchitectureAPI.Application.DTOs.Project.Responses;
using CleanArchitectureAPI.Domain.Entities;

namespace CleanArchitectureAPI.Application.Interfaces
{
	public interface IProjectService
	{
		Task<Result<ProjectDetailResponse>> GetByIdAsync(int id);
		Task<Result<IEnumerable<ProjectResponse>>> GetAllProjectByUserIdAsync(int userId);
		Task<Result<ProjectResponse>> CreateAsync(CreateProjectRequest dto);
		Task<Result<bool>> UpdateAsync(int id, UpdateProjectRequest dto);
		Task<Result<bool>> SoftDeleteAsync(int id);
		Task<Result<bool>> CheckProjectKeyDuplicate(string projectKey);

		// User-Project management methods
		Task<Result<bool>> AddUserToProjectAsync(int userId, int projectId, int? roleId = null);
		Task<Result<bool>> RemoveUserFromProjectAsync(int userId, int projectId);
		Task<Result<bool>> UpdateUserRoleInProjectAsync(int userId, int projectId, int? roleId);
		Task<Result<IEnumerable<ProjectMemberResponse>>> GetProjectMembersAsync(int projectId);
	}
}
