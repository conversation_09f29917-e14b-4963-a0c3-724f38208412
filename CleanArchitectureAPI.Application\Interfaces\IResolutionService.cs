﻿using CleanArchitecture.API.Requests.Resolution;
using CleanArchitectureAPI.Application.Common;
using CleanArchitectureAPI.Application.DTOs.Resolution.Responses;
using CleanArchitectureAPI.Application.Requests.ResolutionRequests;

namespace CleanArchitectureAPI.Application.Interfaces
{
    public interface IResolutionService
    {
        // For business operations - only active resolutions
        Task<Result<IEnumerable<ResolutionResponse>>> GetAllActiveByProjectKeyAsync(string projectKey);

        // For admin/history - include inactive resolutions
        Task<Result<IEnumerable<ResolutionResponse>>> GetAllByProjectKeyAsync(string projectKey);

        Task<Result<ResolutionResponse>> GetByIdAsync(int id);
        Task<Result<ResolutionResponse>> CreateAsync(CreateResolutionRequest request);
        Task<Result<bool>> UpdateAsync(int id, UpdateResolutionRequest request);
        Task<Result<bool>> SoftDeleteAsync(int id);
        Task<Result<bool>> ReorderAsync(ReorderResolutionRequest request);
    }
}