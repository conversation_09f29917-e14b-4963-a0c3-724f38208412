﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CleanArchitecture.API.Requests.Category;
using CleanArchitecture.API.Requests.Status;
using CleanArchitectureAPI.Application.Common;
using CleanArchitectureAPI.Application.DTOs.Status.Responses;
using CleanArchitectureAPI.Application.Requests.StatusRequests;
using CleanArchitectureAPI.Domain.Entities;

namespace CleanArchitectureAPI.Application.Interfaces
{
	public interface IStatusService
	{
		Task<Result<StatusResponse>> GetByIdAsync(int id);

		// For business operations - only active statuses
		Task<Result<IEnumerable<StatusResponse>>> GetAllActiveByProjectKeyAsync(string projectKey);

		// For admin/history - include inactive statuses
		Task<Result<IEnumerable<StatusResponse>>> GetAllByProjectKeyAsync(string projectKey);

		Task<Result<StatusResponse>> CreateAsync(CreateStatusRequest request);
		Task<Result<bool>> UpdateAsync(int id, UpdateStatusRequest request);
		Task<Result<bool>> SoftDeleteAsync(int id);
		Task<Result<bool>> ReorderAsync(ReorderStatusRequest request);
	}
}
