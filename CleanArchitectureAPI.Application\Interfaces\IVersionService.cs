﻿using CleanArchitecture.API.Requests.Version;
using CleanArchitectureAPI.Application.Common;
using CleanArchitectureAPI.Application.DTOs.Version.Responses;
using CleanArchitectureAPI.Application.Requests.VersionRequests;

namespace CleanArchitectureAPI.Application.Interfaces
{
    public interface IVersionService
    {
        // For business operations - only active versions
        Task<Result<IEnumerable<VersionResponse>>> GetAllActiveByProjectKeyAsync(string projectKey);

        // For admin/history - include inactive versions
        Task<Result<IEnumerable<VersionResponse>>> GetAllByProjectKeyAsync(string projectKey);

        Task<Result<VersionResponse>> GetByIdAsync(int id);
        Task<Result<VersionResponse>> CreateAsync(CreateVersionRequest request);
        Task<Result<bool>> UpdateAsync(int id, UpdateVersionRequest request);
        Task<Result<bool>> SoftDeleteAsync(int id);
        Task<Result<bool>> ReorderAsync(ReorderVersionRequest request);
    }
}