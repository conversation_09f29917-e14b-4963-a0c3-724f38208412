using CleanArchitectureAPI.Application.DTOs;

namespace CleanArchitectureAPI.Application.Interfaces
{
    public interface IWorklogService
    {
        // Basic CRUD operations
        Task<WorklogDto> CreateWorklogAsync(CreateWorklogDto createWorklogDto);
        Task<WorklogDto?> GetWorklogByIdAsync(int id);
        Task<IEnumerable<WorklogDto>> GetWorklogsByIssueIdAsync(int issueId);
        Task<IEnumerable<WorklogDto>> GetWorklogsByAuthorIdAsync(int authorId);
        Task<WorklogPagedResultDto> GetWorklogsPagedAsync(int issueId, int pageNumber, int pageSize);
        Task<WorklogDto> UpdateWorklogAsync(int id, UpdateWorklogDto updateWorklogDto);
        Task<bool> DeleteWorklogAsync(int id);

        // Time tracking
        Task<WorklogSummaryDto> GetWorklogSummaryAsync(int issueId);
        Task<int> GetTotalTimeWorkedAsync(int issueId);
        Task<int> GetTotalTimeWorkedByAuthorAsync(int issueId, int authorId);
        Task<Dictionary<int, int>> GetTimeWorkedByIssuesAsync(IEnumerable<int> issueIds);

        // Date range queries
        Task<IEnumerable<WorklogDto>> GetWorklogsByDateRangeAsync(int issueId, DateTime fromDate, DateTime toDate);
        Task<IEnumerable<WorklogDto>> GetWorklogsByAuthorAndDateRangeAsync(int authorId, DateTime fromDate, DateTime toDate);

        // Statistics and reporting
        Task<IEnumerable<WorklogDto>> GetRecentWorklogsAsync(int issueId, int count = 10);
        Task<Dictionary<string, object>> GetWorklogStatisticsAsync(int issueId);
        Task<Dictionary<string, object>> GetAuthorWorklogStatisticsAsync(int authorId, DateTime? fromDate = null, DateTime? toDate = null);

        // Validation
        Task<bool> CanUserEditWorklogAsync(int worklogId, int userId);
        Task<bool> CanUserDeleteWorklogAsync(int worklogId, int userId);
        Task<bool> IsWorklogValidAsync(CreateWorklogDto worklogDto);
        Task<bool> IsWorklogValidAsync(UpdateWorklogDto worklogDto);
    }
}
