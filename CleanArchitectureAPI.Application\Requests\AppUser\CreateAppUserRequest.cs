// This file has been moved to CleanArchitectureAPI.Application.DTOs.AppUser.Requests.CreateAppUserRequest
// Please update your using statements to use the new location
using CleanArchitectureAPI.Application.DTOs.AppUser.Requests;

namespace CleanArchitectureAPI.Application.Requests.AppUser
{
    // This class is now available in CleanArchitectureAPI.Application.DTOs.AppUser.Requests
    // Keeping this for backward compatibility - please migrate to the new location
    public class CreateAppUserRequest : CleanArchitectureAPI.Application.DTOs.AppUser.Requests.CreateAppUserRequest
    {
    }
}
