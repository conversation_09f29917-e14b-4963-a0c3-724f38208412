﻿// This file has been moved to CleanArchitectureAPI.Application.DTOs.Auth.Requests.RegisterRequest
// Please update your using statements to use the new location
using CleanArchitectureAPI.Application.DTOs.Auth.Requests;

namespace CleanArchitectureAPI.Application.Requests.Auth
{
    // This class is now available in CleanArchitectureAPI.Application.DTOs.Auth.Requests
    // Keeping this for backward compatibility - please migrate to the new location
    public class RegisterUserRequest : CleanArchitectureAPI.Application.DTOs.Auth.Requests.RegisterUserRequest
    {
    }
}
