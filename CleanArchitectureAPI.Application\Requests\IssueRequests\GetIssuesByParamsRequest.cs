using System.ComponentModel.DataAnnotations;

namespace CleanArchitectureAPI.Application.Requests.IssueRequests
{
    public class GetIssuesByParamsRequest
    {
        // Project filtering
        [Required(ErrorMessage = "Project key is required")]
        public string ProjectKey { get; set; } = string.Empty;

        // User filtering
        public int? AssigneeId { get; set; }
        public int? ReporterId { get; set; }

        // Status/Type filtering
        public List<int>? StatusIds { get; set; }
        public List<int>? IssueTypeIds { get; set; }
        public List<int>? PriorityIds { get; set; }

        // Collections filtering
        public List<int>? CategoryIds { get; set; }
        public List<int>? MilestoneIds { get; set; }
        public List<int>? VersionIds { get; set; }

        // Date filtering
        public DateTime? DueDateFrom { get; set; }
        public DateTime? DueDateTo { get; set; }
        public DateTime? CreatedDateFrom { get; set; }
        public DateTime? CreatedDateTo { get; set; }

        // Text search
        public string? SearchText { get; set; }

        // Pagination
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 20;

        // Sorting
        public string? SortBy { get; set; } = "CreatedAt";
        public string? SortDirection { get; set; } = "desc"; // asc, desc

        // Include options
        public bool IncludeInactive { get; set; } = false;
        public bool IncludeDetails { get; set; } = true;
    }
}
