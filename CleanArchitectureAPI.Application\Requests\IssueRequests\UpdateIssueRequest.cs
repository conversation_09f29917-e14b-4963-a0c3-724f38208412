using System.ComponentModel.DataAnnotations;

namespace CleanArchitectureAPI.Application.Requests.IssueRequests
{
    public class UpdateIssueRequest
    {
        [Required(ErrorMessage = "Subject is required")]
        [StringLength(500, ErrorMessage = "Subject cannot exceed 500 characters")]
        public string Subject { get; set; } = string.Empty;

        [StringLength(5000, ErrorMessage = "Description cannot exceed 5000 characters")]
        public string? Description { get; set; }

        // Optional assignee - có thể null hoặc 0 (sẽ được xử lý thành null)
        public int? AssigneeId { get; set; }

        // Dates - optional
        public DateTime? DueDate { get; set; }

        // Required fields - KHÔNG được null hoặc 0
        [Required(ErrorMessage = "Issue type is required")]
        [Range(1, int.MaxValue, ErrorMessage = "Issue type must be greater than 0")]
        public int IssueTypeId { get; set; }

        [Required(ErrorMessage = "Status is required")]
        [Range(1, int.MaxValue, ErrorMessage = "Status must be greater than 0")]
        public int StatusId { get; set; }

        [Required(ErrorMessage = "Priority is required")]
        [Range(1, int.MaxValue, ErrorMessage = "Priority must be greater than 0")]
        public int PriorityId { get; set; }

        // Collections (optional) - có thể chứa 0, sẽ được filter
        public List<int>? CategoryIds { get; set; }
        public List<int>? MilestoneIds { get; set; }
        public List<int>? VersionIds { get; set; }
    }

    public class AssignIssueRequest
    {
        // AssigneeId có thể là 0 để unassign, hoặc > 0 để assign
        public int AssigneeId { get; set; }
    }

    public class ChangeIssueStatusRequest
    {
        [Required(ErrorMessage = "Status ID is required")]
        public int StatusId { get; set; }
    }

    public class ChangeIssueResolutionRequest
    {
        [Required(ErrorMessage = "Resolution ID is required")]
        public int ResolutionId { get; set; }
    }
}
