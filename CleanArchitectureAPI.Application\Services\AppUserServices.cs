﻿using CleanArchitecture.Shared.Helpers;
using CleanArchitectureAPI.Application.Common;
using CleanArchitectureAPI.Application.DTOs.AppUser.Requests;
using CleanArchitectureAPI.Application.DTOs.AppUser.Responses;
using CleanArchitectureAPI.Application.Interfaces;
using CleanArchitectureAPI.Domain.Entities.User;
using CleanArchitectureAPI.Domain.Interfaces.User;
using CleanArchitectureAPI.Shared.Helpers;
using Mapster;

namespace CleanArchitectureAPI.Application.Services
{
    public class AppUserServices : IAppUserServices
    {
        private readonly IAppUserRepository _appUserRepository;
        private readonly ICurrentUserService _currentUserService;

        public AppUserServices(IAppUserRepository appUserRepository, ICurrentUserService currentUserService)
        {
            _appUserRepository = appUserRepository;
            _currentUserService = currentUserService;
        }

        public async Task<Result<IEnumerable<AppUserResponse>>> GetAllAsync()
        {
            var users = await _appUserRepository.GetAllActiveAsync();
            var userDtos = users.Adapt<IEnumerable<AppUserResponse>>();
            return Result<IEnumerable<AppUserResponse>>.Success(userDtos);
        }

        public async Task<Result<AppUserResponse>> GetByIdAsync(int id)
        {
            var user = await _appUserRepository.GetByIdAsync(id);
            if (user == null || user.IsDeleted)
                return Result<AppUserResponse>.Failure(ErrorCode.NotFound, "Không tìm thấy người dùng");

            return Result<AppUserResponse>.Success(user.Adapt<AppUserResponse>());
        }

        public async Task<Result<AppUserResponse>> CreateAsync(CreateAppUserRequest request)
        {
            // Check if email already exists
            if (await _appUserRepository.IsEmailExistsAsync(request.Email))
                return Result<AppUserResponse>.Failure(ErrorCode.Conflict, "Email đã tồn tại trong hệ thống");

            var user = new AppUser
            {
                FullName = request.FullName,
                Email = request.Email.ToLower(),
                PasswordHash = PasswordHelper.HassPassword(request.Password),
                Phone = request.Phone,
                Bio = request.Bio,
                Address = request.Address,
                Status = request.Status,
                CreatedAt = DateTime.UtcNow,
                IsActive = true,
                LastActive = DateTime.UtcNow,
                LastLoginTime = DateTime.UtcNow
            };

            await _appUserRepository.CreateAsync(user);
            return Result<AppUserResponse>.Success(user.Adapt<AppUserResponse>());
        }

        public async Task<Result<AppUserResponse>> UpdateAsync(int id, UpdateAppUserRequest request)
        {
            var user = await _appUserRepository.GetByIdAsync(id);
            if (user == null || user.IsDeleted)
                return Result<AppUserResponse>.Failure(ErrorCode.NotFound, "Không tìm thấy người dùng");

            user.FullName = request.FullName;
            user.Phone = request.Phone;
            user.Bio = request.Bio;
            user.Address = request.Address;
            user.Status = request.Status;
            user.LastModifiedAt = DateTime.UtcNow;
            user.LastModifiedById = _currentUserService.GetUserId();

            await _appUserRepository.UpdateAsync(user);
            return Result<AppUserResponse>.Success(user.Adapt<AppUserResponse>());
        }

        public async Task<Result<bool>> DeleteAsync(int id)
        {
            var user = await _appUserRepository.GetByIdAsync(id);
            if (user == null || user.IsDeleted)
                return Result<bool>.Failure(ErrorCode.NotFound, "Không tìm thấy người dùng");

            user.IsDeleted = true;
            user.DeletedAt = DateTime.UtcNow;
            user.DeletedById = _currentUserService.GetUserId();

            await _appUserRepository.UpdateAsync(user);
            return Result<bool>.Success(true);
        }

        public async Task<Result<AppUserResponse>> UpdateProfileAsync(int id, UpdateProfileRequest request)
        {
            var user = await _appUserRepository.GetByIdAsync(id);
            if (user == null || user.IsDeleted)
                return Result<AppUserResponse>.Failure(ErrorCode.NotFound, "Không tìm thấy người dùng");

            user.FullName = request.FullName;
            user.Phone = request.Phone;
            user.Bio = request.Bio;
            user.Address = request.Address;
            user.LastModifiedAt = DateTime.UtcNow;
            user.LastModifiedById = _currentUserService.GetUserId();

            await _appUserRepository.UpdateAsync(user);
            return Result<AppUserResponse>.Success(user.Adapt<AppUserResponse>());
        }

        public async Task<Result<bool>> ChangePasswordAsync(int id, ChangePasswordRequest request)
        {
            var user = await _appUserRepository.GetByIdAsync(id);
            if (user == null || user.IsDeleted)
                return Result<bool>.Failure(ErrorCode.NotFound, "Không tìm thấy người dùng");

            // Verify current password
            if (!BCrypt.Net.BCrypt.Verify(request.CurrentPassword, user.PasswordHash))
                return Result<bool>.Failure(ErrorCode.InvalidCredentials, "Mật khẩu hiện tại không chính xác");

            // Update password
            user.PasswordHash = PasswordHelper.HassPassword(request.NewPassword);
            user.LastModifiedAt = DateTime.UtcNow;
            user.LastModifiedById = _currentUserService.GetUserId();

            await _appUserRepository.UpdateAsync(user);
            return Result<bool>.Success(true);
        }

        public async Task<Result<AppUserResponse>> GetCurrentUserAsync()
        {
            var currentUserId = _currentUserService.GetUserId();
            if (currentUserId == null)
                return Result<AppUserResponse>.Failure(ErrorCode.Unauthorized, "Người dùng chưa đăng nhập");

            return await GetByIdAsync(currentUserId);
        }

        public async Task<Result<string>> SetHasCompletedInitialSetupAsync(int userId)
        {
            var user = await _appUserRepository.GetByIdAsync(userId);
            if (user == null || user.IsDeleted)
                return Result<string>.Failure(ErrorCode.NotFound, "Không tìm thấy người dùng");

            if (user.HasCompletedInitialSetup)
            {
                // Nếu đã completed rồi, vẫn trả về token mới
                var existingToken = JwtHelper.GenerateToken(user, "your-very-secret-key-here-123456");
                return Result<string>.Success(existingToken);
            }

            user.HasCompletedInitialSetup = true;
            user.LastModifiedAt = DateTime.UtcNow;
            user.LastModifiedById = _currentUserService.GetUserId();

            await _appUserRepository.UpdateAsync(user);

            // Tạo token mới với thông tin đã cập nhật
            var newToken = JwtHelper.GenerateToken(user, "your-very-secret-key-here-123456");
            return Result<string>.Success(newToken);
        }
        public async Task<Result<IEnumerable<AppUserResponse>>> GetUsersByProjectKeyAsync(string projectKey)
        {
            var users = await _appUserRepository.GetUsersByProjectKeyAsync(projectKey);
            var userDtos = users.Adapt<IEnumerable<AppUserResponse>>();
            return Result<IEnumerable<AppUserResponse>>.Success(userDtos);
        }

        public async Task<Result<IEnumerable<AppUserResponse>>> GetAllActiveByProjectKeyAsync(string projectKey)
        {
            var users = await _appUserRepository.GetAllActiveByProjectKeyAsync(projectKey);
            var userDtos = users.Adapt<IEnumerable<AppUserResponse>>();
            return Result<IEnumerable<AppUserResponse>>.Success(userDtos);
        }
	}
}

