using CleanArchitectureAPI.Application.DTOs;
using CleanArchitectureAPI.Application.Interfaces;
using CleanArchitectureAPI.Domain.Interfaces;
using CleanArchitectureAPI.Entities.Issue;
using CleanArchitectureAPI.Domain.Entities.User;

namespace CleanArchitectureAPI.Application.Services
{
    public class IssueHistoryService : IIssueHistoryService
    {
        private readonly ICommentRepository _commentRepository;
        private readonly IChangeGroupRepository _changeGroupRepository;
        private readonly IChangeItemRepository _changeItemRepository;
        private readonly ICurrentUserService _currentUserService;

        public IssueHistoryService(
            ICommentRepository commentRepository,
            IChangeGroupRepository changeGroupRepository,
            IChangeItemRepository changeItemRepository,
            ICurrentUserService currentUserService)
        {
            _commentRepository = commentRepository;
            _changeGroupRepository = changeGroupRepository;
            _changeItemRepository = changeItemRepository;
            _currentUserService = currentUserService;
        }

        #region Comment Operations

        public async Task<CommentDto> CreateCommentAsync(CreateCommentDto createCommentDto)
        {
            var currentUserId = _currentUserService.GetUserId();
            
            var comment = new Comment
            {
                IssueId = createCommentDto.IssueId,
                AuthorId = currentUserId,
                ActionBody = createCommentDto.ActionBody,
                CommentType = createCommentDto.CommentType,
                ChangeGroupId = createCommentDto.ChangeGroupId,
                ProjectKey = "DEFAULT", // TODO: Get from context
                CreatedAt = DateTime.UtcNow,
                IsActive = true,
                Issue = null!, // Will be set by EF Core
                Author = null! // Will be set by EF Core
            };

            var createdComment = await _commentRepository.CreateAsync(comment);
            var commentWithDetails = await _commentRepository.GetByIdAsync(createdComment.Id);
            
            return MapToCommentDto(commentWithDetails!);
        }

        public async Task<CommentDto?> GetCommentByIdAsync(int id)
        {
            var comment = await _commentRepository.GetByIdAsync(id);
            return comment != null ? MapToCommentDto(comment) : null;
        }

        public async Task<IEnumerable<CommentDto>> GetCommentsByIssueIdAsync(int issueId)
        {
            var comments = await _commentRepository.GetByIssueIdAsync(issueId);
            return comments.Select(MapToCommentDto);
        }

        public async Task<CommentPagedResultDto> GetCommentsPagedAsync(int issueId, int pageNumber, int pageSize)
        {
            var comments = await _commentRepository.GetPagedAsync(issueId, pageNumber, pageSize);
            var totalCount = await _commentRepository.GetCountByIssueIdAsync(issueId);

            return new CommentPagedResultDto
            {
                Comments = comments.Select(MapToCommentDto),
                TotalCount = totalCount,
                PageNumber = pageNumber,
                PageSize = pageSize
            };
        }

        public async Task<CommentDto> UpdateCommentAsync(int id, UpdateCommentDto updateCommentDto)
        {
            var comment = await _commentRepository.GetByIdAsync(id);
            if (comment == null)
                throw new ArgumentException("Comment not found");

            comment.ActionBody = updateCommentDto.ActionBody;
            comment.LastModifiedAt = DateTime.UtcNow;
            comment.LastModifiedById = _currentUserService.GetUserId();

            var updatedComment = await _commentRepository.UpdateAsync(comment);
            return MapToCommentDto(updatedComment);
        }

        public async Task<bool> DeleteCommentAsync(int id)
        {
            return await _commentRepository.DeleteAsync(id);
        }

        #endregion

        #region ChangeGroup Operations

        public async Task<ChangeGroupDto> CreateChangeGroupAsync(CreateChangeGroupDto createChangeGroupDto)
        {
            var currentUserId = _currentUserService.GetUserId();
            
            var changeGroup = new ChangeGroup
            {
                IssueId = createChangeGroupDto.IssueId,
                AuthorId = currentUserId,
                Description = createChangeGroupDto.Description,
                ChangeType = createChangeGroupDto.ChangeType,
                ProjectKey = "DEFAULT", // TODO: Get from context
                CreatedAt = DateTime.UtcNow,
                IsActive = true,
                Issue = null!, // Will be set by EF Core
                Author = null! // Will be set by EF Core
            };

            var createdChangeGroup = await _changeGroupRepository.CreateAsync(changeGroup);

            // Create change items
            if (createChangeGroupDto.ChangeItems.Any())
            {
                var changeItems = createChangeGroupDto.ChangeItems.Select((item, index) => new ChangeItem
                {
                    GroupId = createdChangeGroup.Id,
                    FieldType = item.FieldType,
                    Field = item.Field,
                    FieldDisplayName = item.FieldDisplayName,
                    OldValue = item.OldValue,
                    OldString = item.OldString,
                    NewValue = item.NewValue,
                    NewString = item.NewString,
                    DisplayOrder = item.DisplayOrder > 0 ? item.DisplayOrder : index,
                    ProjectKey = "DEFAULT", // TODO: Get from context
                    CreatedAt = DateTime.UtcNow,
                    IsActive = true,
                    ChangeGroup = null! // Will be set by EF Core
                });

                await _changeItemRepository.CreateBulkAsync(changeItems);
            }

            var changeGroupWithDetails = await _changeGroupRepository.GetByIdWithItemsAsync(createdChangeGroup.Id);
            return MapToChangeGroupDto(changeGroupWithDetails!);
        }

        public async Task<ChangeGroupDto?> GetChangeGroupByIdAsync(int id)
        {
            var changeGroup = await _changeGroupRepository.GetByIdWithItemsAsync(id);
            return changeGroup != null ? MapToChangeGroupDto(changeGroup) : null;
        }

        public async Task<IEnumerable<ChangeGroupDto>> GetChangeGroupsByIssueIdAsync(int issueId)
        {
            var changeGroups = await _changeGroupRepository.GetByIssueIdWithItemsAsync(issueId);
            return changeGroups.Select(MapToChangeGroupDto);
        }

        public async Task<ChangeGroupPagedResultDto> GetChangeGroupsPagedAsync(int issueId, int pageNumber, int pageSize)
        {
            var changeGroups = await _changeGroupRepository.GetPagedAsync(issueId, pageNumber, pageSize);
            var totalCount = await _changeGroupRepository.GetCountByIssueIdAsync(issueId);

            return new ChangeGroupPagedResultDto
            {
                ChangeGroups = changeGroups.Select(MapToChangeGroupDto),
                TotalCount = totalCount,
                PageNumber = pageNumber,
                PageSize = pageSize
            };
        }

        public async Task<ChangeGroupDto> UpdateChangeGroupAsync(int id, UpdateChangeGroupDto updateChangeGroupDto)
        {
            var changeGroup = await _changeGroupRepository.GetByIdAsync(id);
            if (changeGroup == null)
                throw new ArgumentException("ChangeGroup not found");

            changeGroup.Description = updateChangeGroupDto.Description;
            changeGroup.ChangeType = updateChangeGroupDto.ChangeType;
            changeGroup.LastModifiedAt = DateTime.UtcNow;
            changeGroup.LastModifiedById = _currentUserService.GetUserId();

            var updatedChangeGroup = await _changeGroupRepository.UpdateAsync(changeGroup);
            var changeGroupWithDetails = await _changeGroupRepository.GetByIdWithItemsAsync(updatedChangeGroup.Id);
            return MapToChangeGroupDto(changeGroupWithDetails!);
        }

        public async Task<bool> DeleteChangeGroupAsync(int id)
        {
            return await _changeGroupRepository.DeleteAsync(id);
        }

        #endregion

        #region ChangeItem Operations

        public async Task<ChangeItemDto> CreateChangeItemAsync(int groupId, CreateChangeItemDto createChangeItemDto)
        {
            var changeItem = new ChangeItem
            {
                GroupId = groupId,
                FieldType = createChangeItemDto.FieldType,
                Field = createChangeItemDto.Field,
                FieldDisplayName = createChangeItemDto.FieldDisplayName,
                OldValue = createChangeItemDto.OldValue,
                OldString = createChangeItemDto.OldString,
                NewValue = createChangeItemDto.NewValue,
                NewString = createChangeItemDto.NewString,
                DisplayOrder = createChangeItemDto.DisplayOrder,
                ProjectKey = "DEFAULT", // TODO: Get from context
                CreatedAt = DateTime.UtcNow,
                IsActive = true,
                ChangeGroup = null! // Will be set by EF Core
            };

            var createdChangeItem = await _changeItemRepository.CreateAsync(changeItem);
            return MapToChangeItemDto(createdChangeItem);
        }

        public async Task<ChangeItemDto?> GetChangeItemByIdAsync(int id)
        {
            var changeItem = await _changeItemRepository.GetByIdAsync(id);
            return changeItem != null ? MapToChangeItemDto(changeItem) : null;
        }

        public async Task<IEnumerable<ChangeItemDto>> GetChangeItemsByGroupIdAsync(int groupId)
        {
            var changeItems = await _changeItemRepository.GetByGroupIdAsync(groupId);
            return changeItems.Select(MapToChangeItemDto);
        }

        public async Task<ChangeItemDto> UpdateChangeItemAsync(int id, UpdateChangeItemDto updateChangeItemDto)
        {
            var changeItem = await _changeItemRepository.GetByIdAsync(id);
            if (changeItem == null)
                throw new ArgumentException("ChangeItem not found");

            changeItem.FieldType = updateChangeItemDto.FieldType;
            changeItem.Field = updateChangeItemDto.Field;
            changeItem.FieldDisplayName = updateChangeItemDto.FieldDisplayName;
            changeItem.OldValue = updateChangeItemDto.OldValue;
            changeItem.OldString = updateChangeItemDto.OldString;
            changeItem.NewValue = updateChangeItemDto.NewValue;
            changeItem.NewString = updateChangeItemDto.NewString;
            changeItem.DisplayOrder = updateChangeItemDto.DisplayOrder;
            changeItem.LastModifiedAt = DateTime.UtcNow;
            changeItem.LastModifiedById = _currentUserService.GetUserId();

            var updatedChangeItem = await _changeItemRepository.UpdateAsync(changeItem);
            return MapToChangeItemDto(updatedChangeItem);
        }

        public async Task<bool> DeleteChangeItemAsync(int id)
        {
            return await _changeItemRepository.DeleteAsync(id);
        }

        #endregion

        #region Field History

        public async Task<IEnumerable<FieldHistoryDto>> GetFieldHistoryAsync(int issueId)
        {
            var changeGroups = await _changeGroupRepository.GetByIssueIdWithItemsAsync(issueId);
            
            var fieldGroups = changeGroups
                .SelectMany(cg => cg.ChangeItems)
                .GroupBy(ci => ci.Field)
                .Select(g => new FieldHistoryDto
                {
                    Field = g.Key,
                    FieldDisplayName = g.First().FieldDisplayName,
                    Changes = g.OrderByDescending(ci => ci.CreatedAt).Select(MapToChangeItemDto)
                });

            return fieldGroups;
        }

        public async Task<FieldHistoryDto?> GetFieldHistoryByFieldAsync(int issueId, string field)
        {
            var changeItems = await _changeItemRepository.GetByFieldAsync(issueId, field);
            
            if (!changeItems.Any())
                return null;

            return new FieldHistoryDto
            {
                Field = field,
                FieldDisplayName = changeItems.First().FieldDisplayName,
                Changes = changeItems.Select(MapToChangeItemDto)
            };
        }

        #endregion

        #region Issue Change Tracking

        public async Task<ChangeGroupDto> TrackIssueChangesAsync(int issueId, Dictionary<string, (object? oldValue, object? newValue, string? displayName)> changes, string? description = null)
        {
            if (!changes.Any())
                throw new ArgumentException("No changes provided");

            var createChangeGroupDto = new CreateChangeGroupDto
            {
                IssueId = issueId,
                Description = description ?? "Issue updated",
                ChangeType = "System",
                ChangeItems = changes.Select((kvp, index) => new CreateChangeItemDto
                {
                    FieldType = "jira",
                    Field = kvp.Key,
                    FieldDisplayName = kvp.Value.displayName ?? kvp.Key,
                    OldValue = kvp.Value.oldValue?.ToString(),
                    OldString = kvp.Value.oldValue?.ToString(),
                    NewValue = kvp.Value.newValue?.ToString(),
                    NewString = kvp.Value.newValue?.ToString(),
                    DisplayOrder = index
                })
            };

            return await CreateChangeGroupAsync(createChangeGroupDto);
        }

        public async Task<CommentDto> AddSystemCommentAsync(int issueId, string message, int? changeGroupId = null)
        {
            var createCommentDto = new CreateCommentDto
            {
                IssueId = issueId,
                ActionBody = message,
                CommentType = "System",
                ChangeGroupId = changeGroupId
            };

            return await CreateCommentAsync(createCommentDto);
        }

        #endregion

        #region Statistics

        public async Task<int> GetTotalCommentsCountAsync(int issueId)
        {
            return await _commentRepository.GetCountByIssueIdAsync(issueId);
        }

        public async Task<int> GetTotalChangeGroupsCountAsync(int issueId)
        {
            return await _changeGroupRepository.GetCountByIssueIdAsync(issueId);
        }

        public async Task<IEnumerable<ChangeGroupDto>> GetRecentChangesAsync(int issueId, int count = 10)
        {
            var changeGroups = await _changeGroupRepository.GetPagedAsync(issueId, 1, count);
            return changeGroups.Select(MapToChangeGroupDto);
        }

        #endregion

        #region Mapping Methods

        private static CommentDto MapToCommentDto(Comment comment)
        {
            return new CommentDto
            {
                Id = comment.Id,
                IssueId = comment.IssueId,
                AuthorId = comment.AuthorId,
                AuthorName = comment.Author != null ? comment.Author.FullName : "Unknown",
                ActionBody = comment.ActionBody,
                CommentType = comment.CommentType,
                ChangeGroupId = comment.ChangeGroupId,
                IsEdited = comment.IsEdited,
                LastEditedAt = comment.LastEditedAt,
                CreatedAt = comment.CreatedAt,
                LastModifiedAt = comment.LastModifiedAt
            };
        }

        private static ChangeGroupDto MapToChangeGroupDto(ChangeGroup changeGroup)
        {
            return new ChangeGroupDto
            {
                Id = changeGroup.Id,
                IssueId = changeGroup.IssueId,
                AuthorId = changeGroup.AuthorId,
                AuthorName = changeGroup.Author != null ? changeGroup.Author.FullName : "Unknown",
                Description = changeGroup.Description,
                ChangeType = changeGroup.ChangeType,
                CreatedAt = changeGroup.CreatedAt,
                ChangeItems = changeGroup.ChangeItems?.Select(MapToChangeItemDto) ?? new List<ChangeItemDto>(),
                Comments = changeGroup.Comments?.Select(MapToCommentDto) ?? new List<CommentDto>()
            };
        }

        private static ChangeItemDto MapToChangeItemDto(ChangeItem changeItem)
        {
            return new ChangeItemDto
            {
                Id = changeItem.Id,
                GroupId = changeItem.GroupId,
                FieldType = changeItem.FieldType,
                Field = changeItem.Field,
                FieldDisplayName = changeItem.FieldDisplayName,
                OldValue = changeItem.OldValue,
                OldString = changeItem.OldString,
                NewValue = changeItem.NewValue,
                NewString = changeItem.NewString,
                DisplayOrder = changeItem.DisplayOrder,
                CreatedAt = changeItem.CreatedAt
            };
        }

        #endregion
    }
}
