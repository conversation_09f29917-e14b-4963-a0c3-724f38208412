using CleanArchitectureAPI.Application.Common;
using CleanArchitectureAPI.Application.DTOs;
using CleanArchitectureAPI.Application.DTOs.Issue.Responses;
using CleanArchitectureAPI.Application.DTOs.Category.Responses;
using CleanArchitectureAPI.Application.DTOs.Milestone.Responses;
using CleanArchitectureAPI.Application.DTOs.Version.Responses;
using CleanArchitectureAPI.Application.DTOs.Common;
using CleanArchitectureAPI.Application.Interfaces;
using CleanArchitectureAPI.Application.Requests.IssueRequests;
using CleanArchitectureAPI.Domain.Interfaces;
using CleanArchitectureAPI.Entities.Issue;
using Mapster;

namespace CleanArchitectureAPI.Application.Services
{
    public class IssueService : IIssueService
    {
        private readonly IIssueRepository _issueRepository;
        private readonly ICurrentUserService _currentUserService;
        private readonly IProjectAccessService _projectAccessService;
        private readonly IIssueCategoryRepository _issueCategoryRepository;
        private readonly IIssueMilestoneRepository _issueMilestoneRepository;
        private readonly IIssueVersionRepository _issueVersionRepository;

        public IssueService(
            IIssueRepository issueRepository,
            ICurrentUserService currentUserService,
            IProjectAccessService projectAccessService,
            IIssueCategoryRepository issueCategoryRepository,
            IIssueMilestoneRepository issueMilestoneRepository,
            IIssueVersionRepository issueVersionRepository)
        {
            _issueRepository = issueRepository;
            _currentUserService = currentUserService;
            _projectAccessService = projectAccessService;
            _issueCategoryRepository = issueCategoryRepository;
            _issueMilestoneRepository = issueMilestoneRepository;
            _issueVersionRepository = issueVersionRepository;
        }

        public async Task<Result<IssueResponse>> GetByProjectIssueKeyAsync(string projectIssueKey)
        {
            var issue = await _issueRepository.GetByProjectIssueKeyWithDetailsAsync(projectIssueKey);
            if (issue == null)
                return Result<IssueResponse>.Failure(ErrorCode.NotFound, "Issue không tồn tại");

            var issueDto = MapToIssueResponse(issue);
            return Result<IssueResponse>.Success(issueDto);
        }

        public async Task<Result<IssueResponse>> CreateAsync(CreateIssueRequest request)
        {
            // Validate project exists
            int? projectId = await _projectAccessService.ResolveProjectIdAsync(request.ProjectKey);
            if (projectId is null)
                return Result<IssueResponse>.Failure(ErrorCode.NotFound, "Project không tồn tại");

            // Validate required IDs are not 0
            if (request.IssueTypeId <= 0)
                return Result<IssueResponse>.Failure(ErrorCode.ValidationError, "Issue type ID phải lớn hơn 0");

            if (request.StatusId <= 0)
                return Result<IssueResponse>.Failure(ErrorCode.ValidationError, "Status ID phải lớn hơn 0");

            if (request.PriorityId <= 0)
                return Result<IssueResponse>.Failure(ErrorCode.ValidationError, "Priority ID phải lớn hơn 0");

            // Handle AssigneeId = 0 as null (unassigned)
            int? assigneeId = request.AssigneeId.HasValue && request.AssigneeId.Value > 0 ? request.AssigneeId.Value : null;

            // Generate next issue number and create projectIssueKey
            var nextIssueNumber = await _issueRepository.GetNextIssueNumberAsync(request.ProjectKey);
            var projectIssueKey = $"{request.ProjectKey}-{nextIssueNumber}";

            // Check if projectIssueKey already exists (should not happen, but safety check)
            if (await _issueRepository.IsProjectIssueKeyExistsAsync(projectIssueKey))
                return Result<IssueResponse>.Failure(ErrorCode.Conflict, "Issue key đã tồn tại");

            // Filter out 0 values from collections
            var validCategoryIds = request.CategoryIds?.Where(id => id > 0).ToList() ?? new List<int>();
            var validMilestoneIds = request.MilestoneIds?.Where(id => id > 0).ToList() ?? new List<int>();
            var validVersionIds = request.VersionIds?.Where(id => id > 0).ToList() ?? new List<int>();

            var issue = new Issue
            {
                ProjectIssueKey = projectIssueKey,
                Subject = request.Subject,
                Description = request.Description,
                ProjectKey = request.ProjectKey,
                ProjectId = projectId.Value,
                AssigneeId = assigneeId,
                ReporterId = _currentUserService.GetUserId(),
                PriorityId = request.PriorityId,
                DueDate = request.DueDate?.ToLocalTime(),
                IssueTypeId = request.IssueTypeId,
                StatusId = request.StatusId,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                CreatedById = _currentUserService.GetUserId()
            };

            await _issueRepository.CreateAsync(issue);

            // Handle categories, milestones, versions collections
            await SaveIssueCollectionsAsync(issue.Id, validCategoryIds, validMilestoneIds, validVersionIds);

            var createdIssue = await _issueRepository.GetByProjectIssueKeyWithDetailsAsync(projectIssueKey);
            var issueDto = MapToIssueResponse(createdIssue!);

            return Result<IssueResponse>.Success(issueDto);
        }

        public async Task<Result<IssueResponse>> UpdateAsync(string projectIssueKey, UpdateIssueRequest request)
        {
            var issue = await _issueRepository.GetByProjectIssueKeyAsync(projectIssueKey);
            if (issue == null)
                return Result<IssueResponse>.Failure(ErrorCode.NotFound, "Issue không tồn tại");

            // Validate required IDs are not 0
            if (request.IssueTypeId <= 0)
                return Result<IssueResponse>.Failure(ErrorCode.ValidationError, "Issue type ID phải lớn hơn 0");

            if (request.StatusId <= 0)
                return Result<IssueResponse>.Failure(ErrorCode.ValidationError, "Status ID phải lớn hơn 0");

            if (request.PriorityId <= 0)
                return Result<IssueResponse>.Failure(ErrorCode.ValidationError, "Priority ID phải lớn hơn 0");

            // Handle AssigneeId = 0 as null (unassigned)
            int? assigneeId = request.AssigneeId.HasValue && request.AssigneeId.Value > 0 ? request.AssigneeId.Value : null;

            // Filter out 0 values from collections
            var validCategoryIds = request.CategoryIds?.Where(id => id > 0).ToList() ?? new List<int>();
            var validMilestoneIds = request.MilestoneIds?.Where(id => id > 0).ToList() ?? new List<int>();
            var validVersionIds = request.VersionIds?.Where(id => id > 0).ToList() ?? new List<int>();

            // Update fields
            issue.Subject = request.Subject;
            issue.Description = request.Description;
            issue.AssigneeId = assigneeId;
            issue.DueDate = request.DueDate?.ToLocalTime();
            issue.IssueTypeId = request.IssueTypeId;
            issue.StatusId = request.StatusId;
            issue.PriorityId = request.PriorityId;
            issue.LastModifiedAt = DateTime.UtcNow;
            issue.LastModifiedById = _currentUserService.GetUserId();

            await _issueRepository.UpdateAsync(issue);

            // Handle categories, milestones, versions collections updates
            await UpdateIssueCollectionsAsync(issue.Id, validCategoryIds, validMilestoneIds, validVersionIds);

            var updatedIssue = await _issueRepository.GetByProjectIssueKeyWithDetailsAsync(projectIssueKey);
            var issueDto = MapToIssueResponse(updatedIssue!);

            return Result<IssueResponse>.Success(issueDto);
        }

        public async Task<Result<bool>> DeleteAsync(string projectIssueKey)
        {
            var issue = await _issueRepository.GetByProjectIssueKeyAsync(projectIssueKey);
            if (issue == null)
                return Result<bool>.Failure(ErrorCode.NotFound, "Issue không tồn tại");

            issue.IsDeleted = true;
            issue.LastModifiedAt = DateTime.UtcNow;
            issue.LastModifiedById = _currentUserService.GetUserId();
            await _issueRepository.UpdateAsync(issue);
            return Result<bool>.Success(true);
        }

        public async Task<Result<IEnumerable<IssueResponse>>> GetAllAsync()
        {
            var issues = await _issueRepository.GetAllWithDetailsAsync();
            var issueDtos = issues.Select(MapToIssueListResponse).ToList();
            return Result<IEnumerable<IssueResponse>>.Success(issueDtos);
        }

        public async Task<Result<IEnumerable<IssueResponse>>> GetAllByProjectKeyAsync(string projectKey)
        {
            var issues = await _issueRepository.GetAllByProjectKeyWithDetailsAsync(projectKey);
            var issueDtos = issues.Select(MapToIssueListResponse).ToList();
            return Result<IEnumerable<IssueResponse>>.Success(issueDtos);
        }

        public async Task<Result<IEnumerable<IssueResponse>>> GetAllByAssigneeAsync(int assigneeId)
        {
            var issues = await _issueRepository.GetAllByAssigneeAsync(assigneeId);
            var issueDtos = issues.Select(MapToIssueListResponse).ToList();
            return Result<IEnumerable<IssueResponse>>.Success(issueDtos);
        }

        public async Task<Result<IEnumerable<IssueResponse>>> GetAllByReporterAsync(int reporterId)
        {
            var issues = await _issueRepository.GetAllByReporterAsync(reporterId);
            var issueDtos = issues.Select(MapToIssueListResponse).ToList();
            return Result<IEnumerable<IssueResponse>>.Success(issueDtos);
        }

        public async Task<Result<IEnumerable<IssueResponse>>> GetAllByStatusAsync(int statusId)
        {
            var issues = await _issueRepository.GetAllByStatusAsync(statusId);
            var issueDtos = issues.Select(MapToIssueListResponse).ToList();
            return Result<IEnumerable<IssueResponse>>.Success(issueDtos);
        }

        public async Task<Result<IEnumerable<IssueResponse>>> GetAllByIssueTypeAsync(int issueTypeId)
        {
            var issues = await _issueRepository.GetAllByIssueTypeAsync(issueTypeId);
            var issueDtos = issues.Select(MapToIssueListResponse).ToList();
            return Result<IEnumerable<IssueResponse>>.Success(issueDtos);
        }

        public async Task<Result<CleanArchitectureAPI.Application.DTOs.Common.PagedResultDto<IssueResponse>>> GetIssuesByParamsAsync(GetIssuesByParamsRequest request)
        {
            // Validate project access
            var projectId = await _projectAccessService.ResolveProjectIdAsync(request.ProjectKey);
            if (!projectId.HasValue)
                return Result<CleanArchitectureAPI.Application.DTOs.Common.PagedResultDto<IssueResponse>>.Failure(ErrorCode.NotFound, "Project không tồn tại hoặc không có quyền truy cập");

            // Filter valid IDs (remove 0 and negative values)
            var validStatusIds = FilterValidIds(request.StatusIds);
            var validIssueTypeIds = FilterValidIds(request.IssueTypeIds);
            var validPriorityIds = FilterValidIds(request.PriorityIds);
            var validCategoryIds = FilterValidIds(request.CategoryIds);
            var validMilestoneIds = FilterValidIds(request.MilestoneIds);
            var validVersionIds = FilterValidIds(request.VersionIds);

            // Call repository with filtered parameters
            var (issues, totalCount) = await _issueRepository.GetIssuesByParamsAsync(
                projectKey: request.ProjectKey,
                assigneeId: request.AssigneeId > 0 ? request.AssigneeId : null,
                reporterId: request.ReporterId > 0 ? request.ReporterId : null,
                statusIds: validStatusIds.Any() ? validStatusIds : null,
                issueTypeIds: validIssueTypeIds.Any() ? validIssueTypeIds : null,
                priorityIds: validPriorityIds.Any() ? validPriorityIds : null,
                categoryIds: validCategoryIds.Any() ? validCategoryIds : null,
                milestoneIds: validMilestoneIds.Any() ? validMilestoneIds : null,
                versionIds: validVersionIds.Any() ? validVersionIds : null,
                dueDateFrom: request.DueDateFrom,
                dueDateTo: request.DueDateTo,
                createdDateFrom: request.CreatedDateFrom,
                createdDateTo: request.CreatedDateTo,
                searchText: request.SearchText,
                includeInactive: request.IncludeInactive,
                includeDetails: request.IncludeDetails,
                sortBy: request.SortBy ?? "CreatedAt",
                sortDirection: request.SortDirection ?? "desc",
                page: Math.Max(1, request.Page),
                pageSize: Math.Max(1, Math.Min(100, request.PageSize)) // Limit max page size to 100
            );

            // Map to DTOs
            var issueDtos = issues.Select(MapToIssueListResponse).ToList();

            // Create paged result
            var pagedResult = new CleanArchitectureAPI.Application.DTOs.Common.PagedResultDto<IssueResponse>(issueDtos, totalCount, request.Page, request.PageSize);

            return Result<CleanArchitectureAPI.Application.DTOs.Common.PagedResultDto<IssueResponse>>.Success(pagedResult);
        }

        public async Task<Result<bool>> SoftDeleteAsync(string projectIssueKey)
        {
            var result = await _issueRepository.SoftDeleteAsync(projectIssueKey);
            if (!result)
                return Result<bool>.Failure(ErrorCode.NotFound, "Issue không tồn tại");

            return Result<bool>.Success(true);
        }

        public async Task<Result<bool>> RestoreAsync(string projectIssueKey)
        {
            var result = await _issueRepository.RestoreAsync(projectIssueKey);
            if (!result)
                return Result<bool>.Failure(ErrorCode.NotFound, "Issue không tồn tại");

            return Result<bool>.Success(true);
        }

        public async Task<Result<bool>> AssignToUserAsync(string projectIssueKey, int assigneeId)
        {
            // Nếu assigneeId = 0, thì unassign (set null)
            if (assigneeId <= 0)
            {
                var unassignResult = await _issueRepository.UnassignAsync(projectIssueKey);
                if (!unassignResult)
                    return Result<bool>.Failure(ErrorCode.NotFound, "Issue không tồn tại");
                return Result<bool>.Success(true);
            }

            // Nếu assigneeId > 0, thì assign
            var result = await _issueRepository.AssignToUserAsync(projectIssueKey, assigneeId);
            if (!result)
                return Result<bool>.Failure(ErrorCode.NotFound, "Issue không tồn tại");

            return Result<bool>.Success(true);
        }

        public async Task<Result<bool>> UnassignAsync(string projectIssueKey)
        {
            var result = await _issueRepository.UnassignAsync(projectIssueKey);
            if (!result)
                return Result<bool>.Failure(ErrorCode.NotFound, "Issue không tồn tại");

            return Result<bool>.Success(true);
        }

        public async Task<Result<bool>> ChangeStatusAsync(string projectIssueKey, int statusId)
        {
            var result = await _issueRepository.ChangeStatusAsync(projectIssueKey, statusId);
            if (!result)
                return Result<bool>.Failure(ErrorCode.NotFound, "Issue không tồn tại");

            return Result<bool>.Success(true);
        }

        //public async Task<Result<bool>> ChangeResolutionAsync(string projectIssueKey, int resolutionId)
        //{
        //    var result = await _issueRepository.ChangeResolutionAsync(projectIssueKey, resolutionId);
        //    if (!result)
        //        return Result<bool>.Failure(ErrorCode.NotFound, "Issue không tồn tại");
        //
        //    return Result<bool>.Success(true);
        //}

        // Helper method để validate chỉ những field BẮT BUỘC
        private Result<bool> ValidateRequiredIds(int issueTypeId, int statusId, int priorityId)
        {
            if (issueTypeId <= 0)
                return Result<bool>.Failure(ErrorCode.ValidationError, "Issue type ID phải lớn hơn 0");

            if (statusId <= 0)
                return Result<bool>.Failure(ErrorCode.ValidationError, "Status ID phải lớn hơn 0");

            if (priorityId <= 0)
                return Result<bool>.Failure(ErrorCode.ValidationError, "Priority ID phải lớn hơn 0");

            return Result<bool>.Success(true);
        }

        // Helper method để xử lý AssigneeId: 0 hoặc null = unassigned
        private int? ProcessAssigneeId(int? assigneeId)
        {
            // Chỉ assign khi assigneeId > 0, còn lại đều là null (unassigned)
            return assigneeId.HasValue && assigneeId.Value > 0 ? assigneeId.Value : null;
        }

        // Helper method để filter collections: loại bỏ các ID <= 0
        private List<int> FilterValidIds(List<int>? ids)
        {
            // Chỉ giữ lại những ID > 0, loại bỏ 0 và số âm
            return ids?.Where(id => id > 0).ToList() ?? new List<int>();
        }

        // Helper method để lưu collections cho issue mới
        private async Task SaveIssueCollectionsAsync(int issueId, List<int> categoryIds, List<int> milestoneIds, List<int> versionIds)
        {
            // Lấy issue để có ProjectKey
            var issue = await _issueRepository.GetByIdAsync(issueId);
            if (issue == null) return;

            // Lưu IssueCategories
            foreach (var categoryId in categoryIds)
            {
                var issueCategory = new IssueCategory
                {
                    ProjectKey = issue.ProjectKey,
                    ProjectId = issue.ProjectId,
                    IssueId = issueId,
                    CategoryId = categoryId,
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow,
                    CreatedById = _currentUserService.GetUserId()
                };
                await _issueCategoryRepository.CreateAsync(issueCategory);
            }

            // Lưu IssueMilestones
            foreach (var milestoneId in milestoneIds)
            {
                var issueMilestone = new IssueMilestone
                {
                    ProjectKey = issue.ProjectKey,
                    ProjectId = issue.ProjectId,
                    IssueId = issueId,
                    MilestoneId = milestoneId,
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow,
                    CreatedById = _currentUserService.GetUserId()
                };
                await _issueMilestoneRepository.CreateAsync(issueMilestone);
            }

            // Lưu IssueVersions
            foreach (var versionId in versionIds)
            {
                var issueVersion = new IssueVersion
                {
                    ProjectKey = issue.ProjectKey,
                    ProjectId = issue.ProjectId,
                    IssueId = issueId,
                    VersionId = versionId,
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow,
                    CreatedById = _currentUserService.GetUserId()
                };
                await _issueVersionRepository.CreateAsync(issueVersion);
            }
        }

        // Helper method để cập nhật collections cho issue
        private async Task UpdateIssueCollectionsAsync(int issueId, List<int> categoryIds, List<int> milestoneIds, List<int> versionIds)
        {
            // Xóa tất cả collections cũ
            await _issueCategoryRepository.DeleteByIssueIdAsync(issueId);
            await _issueMilestoneRepository.DeleteByIssueIdAsync(issueId);
            await _issueVersionRepository.DeleteByIssueIdAsync(issueId);

            // Lưu lại collections mới
            await SaveIssueCollectionsAsync(issueId, categoryIds, milestoneIds, versionIds);
        }
        

        private IssueResponse MapToIssueResponse(Issue issue)
        {
            return new IssueResponse
            {
                Id = issue.Id,
                ProjectIssueKey = issue.ProjectIssueKey,
                Subject = issue.Subject,
                Description = issue.Description,
                ProjectKey = issue.ProjectKey,
                ProjectId = issue.ProjectId,
                AssigneeId = issue.AssigneeId,
                AssigneeName = issue.Assignee?.FullName,
                AssigneeEmail = issue.Assignee?.Email,
                ReporterId = issue.ReporterId,
                ReporterName = issue.Reporter?.FullName ?? "Unknown",
                ReporterEmail = issue.Reporter?.Email ?? "Unknown",
                DueDate = issue.DueDate,
                IssueTypeId = issue.IssueTypeId,
                IssueTypeName = issue.IssueType?.Name ?? "Unknown",
                IssueTypeColor = issue.IssueType?.Color,
                StatusId = issue.StatusId,
                StatusName = issue.Status?.Name ?? "Unknown",
                StatusColor = issue.Status?.Color,
                Categories = issue.IssueCategories?.Select(ic => ic.Category.Adapt<CategoryResponse>()).ToList() ?? [],
                Milestones = issue.IssueMilestones?.Select(im => im.Milestone.Adapt<MilestoneResponse>()).ToList() ?? [],
                Versions = issue.IssueVersions?.Select(iv => iv.Version.Adapt<VersionResponse>()).ToList() ?? [],
                IsActive = issue.IsActive,
                IsDeleted = issue.IsDeleted,
                CreatedAt = issue.CreatedAt,
                CreatedById = issue.CreatedById,
                LastModifiedAt = issue.LastModifiedAt,
                LastModifiedById = issue.LastModifiedById,
                CommentCount = issue.Comments?.Count ?? 0,
                PriorityId = issue.PriorityId,
                PriorityName = issue.Priority?.Name ?? "Unknown",
                PriorityColor = issue.Priority?.Color,
               
            };
        }

        private IssueResponse MapToIssueListResponse(Issue issue)
        {
            return new IssueResponse
            {
				Id = issue.Id,
				ProjectIssueKey = issue.ProjectIssueKey,
				Subject = issue.Subject,
				Description = issue.Description,
				ProjectKey = issue.ProjectKey,
				ProjectId = issue.ProjectId,
				AssigneeId = issue.AssigneeId,
				AssigneeName = issue.Assignee?.FullName,
				AssigneeEmail = issue.Assignee?.Email,
				ReporterId = issue.ReporterId,
				ReporterName = issue.Reporter?.FullName ?? "Unknown",
				ReporterEmail = issue.Reporter?.Email ?? "Unknown",
				DueDate = issue.DueDate,
				IssueTypeId = issue.IssueTypeId,
				IssueTypeName = issue.IssueType?.Name ?? "Unknown",
				IssueTypeColor = issue.IssueType?.Color,
				StatusId = issue.StatusId,
				StatusName = issue.Status?.Name ?? "Unknown",
				StatusColor = issue.Status?.Color,
			    Categories = issue.IssueCategories?.Select(ic => ic.Category.Adapt<CategoryResponse>()).ToList() ?? [],
                Milestones = issue.IssueMilestones?.Select(im => im.Milestone.Adapt<MilestoneResponse>()).ToList() ?? [],
                Versions = issue.IssueVersions?.Select(iv => iv.Version.Adapt<VersionResponse>()).ToList() ?? [],
				IsActive = issue.IsActive,
				IsDeleted = issue.IsDeleted,
				CreatedAt = issue.CreatedAt,
				CreatedById = issue.CreatedById,
				LastModifiedAt = issue.LastModifiedAt,
				LastModifiedById = issue.LastModifiedById,
				CommentCount = issue.Comments?.Count ?? 0,
				PriorityId = issue.PriorityId,
				PriorityName = issue.Priority?.Name ?? "Unknown",
				PriorityColor = issue.Priority?.Color,
			};
        }
    }
}
