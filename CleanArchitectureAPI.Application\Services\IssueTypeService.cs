﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CleanArchitecture.API.Requests;
using CleanArchitecture.API.Requests.IssueType;
using CleanArchitectureAPI.Application.Common;
using CleanArchitectureAPI.Application.DTOs.IssueType.Responses;
using CleanArchitectureAPI.Application.Interfaces;
using CleanArchitectureAPI.Application.Requests.IssueTypeRequests;
using CleanArchitectureAPI.Domain.Entities;
using CleanArchitectureAPI.Domain.Interfaces;
using Mapster;

namespace CleanArchitectureAPI.Application.Services
{
    public class IssueTypeService : IIssueTypeService
    {
        private readonly IIssueTypeRepository _issueTypeRepository;
        private readonly IProjectAccessService _projectAccessService;
        private readonly ICurrentUserService _currentUserService;

        public IssueTypeService(IIssueTypeRepository issueTypeRepository, IProjectAccessService projectAccessService, ICurrentUserService currentUserService)
        {
            _issueTypeRepository = issueTypeRepository;
            _projectAccessService = projectAccessService;
            _currentUserService = currentUserService;
        }

        public async Task<Result<IssueTypeResponse>> CreateAsync(CreateIssueTypeRequest request)
        {
            int? projectId = await _projectAccessService.ResolveProjectIdAsync(request.ProjectKey);
            if (projectId is null)
                return Result<IssueTypeResponse>.Failure(ErrorCode.NotFound, "Project không tồn tại");

            // Check if name is duplicated
            var isNameDuplicated = await _issueTypeRepository.IsNameDuplicated(request.ProjectKey, request.Name);
            if (isNameDuplicated)
            {
                return Result<IssueTypeResponse>.Failure(ErrorCode.Conflict, "Tên loại vấn đề đã tồn tại trong dự án này.");
            }

            var issueType = new IssueType
            {
                Name = request.Name,
                Description = request.Description,
                Color = request.Color,
                ProjectKey = request.ProjectKey,
                Order = await _issueTypeRepository.GetMaxOrderInProjectAsync(request.ProjectKey) + 1,
                ProjectId = projectId.Value,
                CreatedById = _currentUserService.GetUserId(),
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
            };

            await _issueTypeRepository.CreateAsync(issueType);

            return Result<IssueTypeResponse>.Success(issueType.Adapt<IssueTypeResponse>());
        }

        // For business operations - only active issue types
        public async Task<Result<IEnumerable<IssueTypeResponse>>> GetAllActiveByProjectKeyAsync(string projectKey)
        {
            IEnumerable<IssueType> issueTypes = await _issueTypeRepository.GetAllActiveByProjectKeyAsync(projectKey);
            var issueTypeDtos = issueTypes.Adapt<IEnumerable<IssueTypeResponse>>();
            return Result<IEnumerable<IssueTypeResponse>>.Success(issueTypeDtos);
        }

        // For admin/history - include inactive issue types
        public async Task<Result<IEnumerable<IssueTypeResponse>>> GetAllByProjectKeyAsync(string projectKey)
        {
            IEnumerable<IssueType> issueTypes = await _issueTypeRepository.GetAllByProjectKeyAsync(projectKey);
            var issueTypeDtos = issueTypes.Adapt<IEnumerable<IssueTypeResponse>>();
            return Result<IEnumerable<IssueTypeResponse>>.Success(issueTypeDtos);
        }

        public async Task<Result<IssueTypeResponse>> GetByIdAsync(int id)
        {
            var issueType = await _issueTypeRepository.GetByIdAsync(id);
            if (issueType == null)
                return Result<IssueTypeResponse>.Failure(ErrorCode.NotFound, "Không tìm thấy loại vấn đề");

            return Result<IssueTypeResponse>.Success(issueType.Adapt<IssueTypeResponse>());
        }

        public async Task<Result<bool>> ReorderAsync(ReorderIssueTypeRequest request)
        {
            if (string.IsNullOrWhiteSpace(request.ProjectKey) || request.IssueTypeIdsInOrder == null || !request.IssueTypeIdsInOrder.Any())
            {
                return Result<bool>.Failure("Dữ liệu không hợp lệ.");
            }

            await _issueTypeRepository.ReorderIssueTypesAsync(request.ProjectKey, request.IssueTypeIdsInOrder);
            return Result<bool>.Success(true);
        }

        public async Task<Result<bool>> SoftDeleteAsync(int id)
        {
            var issueType = await _issueTypeRepository.GetByIdAsync(id);
            if (issueType == null)
            {
                return Result<bool>.Failure("Không tìm thấy loại vấn đề.");
            }
            issueType.DeletedById = _currentUserService.GetUserId();
            issueType.DeletedAt = DateTime.UtcNow;
            await _issueTypeRepository.SoftDeleteAsync(id);

            return Result<bool>.Success(true);
        }

        public async Task<Result<bool>> UpdateAsync(int id, UpdateIssueTypeRequest request)
        {
            var issueType = await _issueTypeRepository.GetByIdAsync(id);
            if (issueType == null)
            {
                return Result<bool>.Failure("Không tìm thấy loại vấn đề.");
            }

            var existingIssueTypes = await _issueTypeRepository.GetAllByProjectKeyAsync(request.ProjectKey);
            var isNameDuplicated = existingIssueTypes
                .Where(s => s.Id != id && s.ProjectKey == issueType.ProjectKey && s.Name == request.Name)
                .Any();

            if (isNameDuplicated)
            {
                return Result<bool>.Failure("Tên loại vấn đề đã tồn tại trong dự án này.");
            }
            issueType.Name = request.Name;
            issueType.Description = request.Description;
            issueType.Color = request.Color;
            issueType.LastModifiedAt = DateTime.UtcNow;
            issueType.LastModifiedById = _currentUserService.GetUserId();
            issueType.IsActive = request.IsActive;

            await _issueTypeRepository.UpdateAsync(issueType);

            return Result<bool>.Success(true);
        }
    }
}
