using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CleanArchitecture.API.Requests.Milestone;
using CleanArchitectureAPI.Application.Common;
using CleanArchitectureAPI.Application.DTOs.Milestone.Responses;
using CleanArchitectureAPI.Application.Interfaces;
using CleanArchitectureAPI.Application.Requests.MilestoneRequests;
using CleanArchitectureAPI.Domain.Entities;
using CleanArchitectureAPI.Domain.Interfaces;
using Mapster;

namespace CleanArchitectureAPI.Application.Services
{
    public class MilestoneService : IMilestoneService
    {
        private readonly IMilestoneRepository _milestoneRepository;
        private readonly IProjectAccessService _projectAccessService;
        private readonly ICurrentUserService _currentUserService;

        public MilestoneService(IMilestoneRepository milestoneRepository, IProjectAccessService projectAccessService, ICurrentUserService currentUserService)
        {
            _milestoneRepository = milestoneRepository;
            _projectAccessService = projectAccessService;
            _currentUserService = currentUserService;
        }

        public async Task<Result<MilestoneResponse>> CreateAsync(CreateMilestoneRequest request)
        {
            int? projectId = await _projectAccessService.ResolveProjectIdAsync(request.ProjectKey);
            if (projectId is null)
                return Result<MilestoneResponse>.Failure(ErrorCode.NotFound, "Project không tồn tại");

            // Check if name is duplicated
            var isNameDuplicated = await _milestoneRepository.IsNameDuplicated(request.ProjectKey, request.Name);
            if (isNameDuplicated)
            {
                return Result<MilestoneResponse>.Failure(ErrorCode.Conflict, "Tên cột mốc đã tồn tại trong dự án này.");
            }

            var milestone = new Milestone
            {
                Name = request.Name,
                Description = request.Description,
                ProjectKey = request.ProjectKey,
                Order = await _milestoneRepository.GetMaxOrderInProjectAsync(request.ProjectKey) + 1,
                ProjectId = projectId.Value,
                CreatedById = _currentUserService.GetUserId(),
                IsActive = request.IsActive,
                CreatedAt = DateTime.UtcNow,
                StartDate = request.StartDate?.ToLocalTime(),
                EndDate = request.EndDate?.ToLocalTime(),
                IsReleased = request.IsReleased
            };

            await _milestoneRepository.CreateAsync(milestone);

            return Result<MilestoneResponse>.Success(milestone.Adapt<MilestoneResponse>());
        }

        // For business operations - only active milestones
        public async Task<Result<IEnumerable<MilestoneResponse>>> GetAllActiveByProjectKeyAsync(string projectKey)
        {
            IEnumerable<Milestone> milestones = await _milestoneRepository.GetAllActiveByProjectKeyAsync(projectKey);
            var milestoneDtos = milestones.Adapt<IEnumerable<MilestoneResponse>>();
            return Result<IEnumerable<MilestoneResponse>>.Success(milestoneDtos);
        }

        // For admin/history - include inactive milestones
        public async Task<Result<IEnumerable<MilestoneResponse>>> GetAllByProjectKeyAsync(string projectKey)
        {
            IEnumerable<Milestone> milestones = await _milestoneRepository.GetAllByProjectKeyAsync(projectKey);
            var milestoneDtos = milestones.Adapt<IEnumerable<MilestoneResponse>>();
            return Result<IEnumerable<MilestoneResponse>>.Success(milestoneDtos);
        }

        public async Task<Result<MilestoneResponse>> GetByIdAsync(int id)
        {
            var milestone = await _milestoneRepository.GetByIdAsync(id);
            if (milestone == null)
                return Result<MilestoneResponse>.Failure(ErrorCode.NotFound, "Không tìm thấy cột mốc");

            return Result<MilestoneResponse>.Success(milestone.Adapt<MilestoneResponse>());
        }

        public async Task<Result<bool>> ReorderAsync(ReorderMilestoneRequest request)
        {
            if (string.IsNullOrWhiteSpace(request.ProjectKey) || request.MilestoneIdsInOrder == null || !request.MilestoneIdsInOrder.Any())
            {
                return Result<bool>.Failure("Dữ liệu không hợp lệ.");
            }

            await _milestoneRepository.ReorderMilestonesAsync(request.ProjectKey, request.MilestoneIdsInOrder);
            return Result<bool>.Success(true);
        }

        public async Task<Result<bool>> SoftDeleteAsync(int id)
        {
            var milestone = await _milestoneRepository.GetByIdAsync(id);
            if (milestone == null)
            {
                return Result<bool>.Failure("Không tìm thấy cột mốc.");
            }
            milestone.DeletedById = _currentUserService.GetUserId();
            milestone.DeletedAt = DateTime.UtcNow;
            await _milestoneRepository.SoftDeleteAsync(id);

            return Result<bool>.Success(true);
        }

        public async Task<Result<bool>> UpdateAsync(int id, UpdateMilestoneRequest request)
        {
            var milestone = await _milestoneRepository.GetByIdAsync(id);
            if (milestone == null)
            {
                return Result<bool>.Failure("Không tìm thấy cột mốc.");
            }

            var existingMilestones = await _milestoneRepository.GetAllByProjectKeyAsync(request.ProjectKey);
            var isNameDuplicated = existingMilestones
                .Where(s => s.Id != id && s.ProjectKey == milestone.ProjectKey && s.Name == request.Name)
                .Any();

            if (isNameDuplicated)
            {
                return Result<bool>.Failure("Tên cột mốc đã tồn tại trong dự án này.");
            }
            milestone.Name = request.Name;
            milestone.Description = request.Description;
            milestone.LastModifiedAt = DateTime.UtcNow;
            milestone.LastModifiedById = _currentUserService.GetUserId();
            milestone.IsActive = request.IsActive;
            milestone.StartDate = request.StartDate?.ToLocalTime();
            milestone.EndDate = request.EndDate?.ToLocalTime();
            milestone.IsReleased = request.IsReleased;

            await _milestoneRepository.UpdateAsync(milestone);

            return Result<bool>.Success(true);
        }
    }
}
