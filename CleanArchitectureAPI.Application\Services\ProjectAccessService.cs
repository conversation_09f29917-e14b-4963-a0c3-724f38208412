﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using CleanArchitectureAPI.Application.Common;
using CleanArchitectureAPI.Application.Interfaces;
using CleanArchitectureAPI.Domain.Interfaces;

namespace CleanArchitectureAPI.Application.Services
{
	public class ProjectAccessService : IProjectAccessService
	{
		private readonly IProjectRepository _projectRepository;

		public ProjectAccessService(IProjectRepository projectRepository)
		{
			_projectRepository = projectRepository;
		}

		public async Task<int?> ResolveProjectIdAsync(string projectKey)
		{
			var id = await _projectRepository.GetByKeyAsync(projectKey);
			if (id is null)
				throw new Exception("Project không tồn tại");
			return id.Value;
		}
	}
}
