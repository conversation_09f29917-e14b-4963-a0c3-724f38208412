﻿
using CleanArchitecture.API.Requests.Project;
using CleanArchitectureAPI.Application.Common;
using CleanArchitectureAPI.Application.DTOs.Project.Responses;
using CleanArchitectureAPI.Application.Interfaces;
using CleanArchitectureAPI.Domain.Entities;
using CleanArchitectureAPI.Domain.Entities.User;
using CleanArchitectureAPI.Domain.Interfaces;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using CleanArchitectureAPI.Application.DTOs;
using Mapster;

namespace CleanArchitectureAPI.Application.Services
{
	public class ProjectService : IProjectService
	{
		private readonly IProjectRepository _projectRepository;
		private readonly IIssueTypeRepository _issueTypeRepository;
		private readonly IStatusRepository _statusRepository;
		private readonly IPriorityRepository _priorityRepository;
		private readonly ICurrentUserService _currentUserService;
		private readonly IAppUserServices _appUserServices;
		private readonly IUserProjectRepository _userProjectRepository;
		private readonly IRoleRepository _roleRepository;

		public ProjectService(
			IProjectRepository projectRepository,
			IIssueTypeRepository issueTypeRepository,
			IStatusRepository statusRepository,
			IPriorityRepository priorityRepository,
			ICurrentUserService currentUserService,
			IAppUserServices appUserServices,
			IUserProjectRepository userProjectRepository,
			IRoleRepository roleRepository)
		{
			_projectRepository = projectRepository;
			_issueTypeRepository = issueTypeRepository;
			_statusRepository = statusRepository;
			_priorityRepository = priorityRepository;
			_currentUserService = currentUserService;
			_appUserServices = appUserServices;
			_userProjectRepository = userProjectRepository;
			_roleRepository = roleRepository;
		}

        public async Task<Result<bool>> CheckProjectKeyDuplicate(string projectKey)
        {
            bool project = await _projectRepository.IsProjectKeyDuplicatedAsync(projectKey);
            if (project)
                return Result<bool>.Success(true);
            return Result<bool>.Success(false);
        }

        public async Task<Result<ProjectResponse>> CreateAsync(CreateProjectRequest request)
		{
			//Check project exists
			bool projectExists = await _projectRepository.IsProjectKeyDuplicatedAsync(request.ProjectKey);
			if (projectExists) return Result<ProjectResponse>.Failure(ErrorCode.Conflict, "Mã dự án đã tồn tại");

			//basic-info
			Project project = new Project();
			project.Name = request.Name;
			project.ProjectKey = request.ProjectKey;
			project.IsActive = true;

			//audit
			project.CreatedAt = DateTime.UtcNow;
			project.CreatedById = _currentUserService.GetUserId();

			await _projectRepository.CreateAsync(project);

			if (!_currentUserService.HasCompletedInitialSetup())
			{
				await _appUserServices.SetHasCompletedInitialSetupAsync(_currentUserService.GetUserId());
			}

			// Tạo các role mặc định cho project
			var defaultRoles = new List<Role>
			{
				new Role
				{
					Name = "Owner",
					Description = "Project Owner with full permissions",
					ProjectId = project.Id,
					ProjectKey = project.ProjectKey,
					CreatedAt = DateTime.UtcNow,
					CreatedById = project.CreatedById,
					IsActive = true
				},
				new Role
				{
					Name = "Admin",
					Description = "Project Administrator with management permissions",
					ProjectId = project.Id,
					ProjectKey = project.ProjectKey,
					CreatedAt = DateTime.UtcNow,
					CreatedById = project.CreatedById,
					IsActive = true
				},
				new Role
				{
					Name = "Member",
					Description = "Project Member with basic permissions",
					ProjectId = project.Id,
					ProjectKey = project.ProjectKey,
					CreatedAt = DateTime.UtcNow,
					CreatedById = project.CreatedById,
					IsActive = true
				}
			};

			Role ownerRole = null;
			foreach (var role in defaultRoles)
			{
				var createdRole = await _roleRepository.CreateAsync(role);
				if (role.Name == "Owner")
				{
					ownerRole = createdRole;
				}
			}

			// Tự động thêm người tạo vào project với role Owner
			await _userProjectRepository.AddUserToProjectAsync(
				_currentUserService.GetUserId(),
				project.Id,
				ownerRole?.Id
			);

			// Tạo các entity mặc định: IssueType, Status, Priority
			// IssueTypes mặc định
			var defaultIssueTypes = new List<IssueType>
			{
				new IssueType { Name = "Task", ProjectId = project.Id, ProjectKey = project.ProjectKey, CreatedAt = DateTime.UtcNow, CreatedById = project.CreatedById, IsActive = true, Color = "#4B9EFF" },
				new IssueType { Name = "Bug", ProjectId = project.Id, ProjectKey = project.ProjectKey, CreatedAt = DateTime.UtcNow, CreatedById = project.CreatedById, IsActive = true, Color = "#FF4B4B" },
				new IssueType { Name = "Story", ProjectId = project.Id, ProjectKey = project.ProjectKey, CreatedAt = DateTime.UtcNow, CreatedById = project.CreatedById, IsActive = true, Color = "#36B37E" }
			};
			foreach (var issueType in defaultIssueTypes)
			{
				await _issueTypeRepository.CreateAsync(issueType);
			}

			// Statuses mặc định
			var defaultStatuses = new List<Status>
			{
				new Status { Name = "To Do", ProjectId = project.Id, ProjectKey = project.ProjectKey, CreatedAt = DateTime.UtcNow, CreatedById = project.CreatedById, IsActive = true, Order = 1, Color = "#BDBDBD" },
				new Status { Name = "In Progress", ProjectId = project.Id, ProjectKey = project.ProjectKey, CreatedAt = DateTime.UtcNow, CreatedById = project.CreatedById, IsActive = true, Order = 2, Color = "#1976D2" },
				new Status { Name = "Done", ProjectId = project.Id, ProjectKey = project.ProjectKey, CreatedAt = DateTime.UtcNow, CreatedById = project.CreatedById, IsActive = true, Order = 3, Color = "#388E3C" }
			};
			foreach (var status in defaultStatuses)
			{
				await _statusRepository.CreateAsync(status);
			}

			// Priorities mặc định
			var defaultPriorities = new List<Priority>
			{
				new Priority { Name = "Low", ProjectId = project.Id, ProjectKey = project.ProjectKey, CreatedAt = DateTime.UtcNow, CreatedById = project.CreatedById, IsActive = true, Order = 1, Color = "#8BC34A" },
				new Priority { Name = "Medium", ProjectId = project.Id, ProjectKey = project.ProjectKey, CreatedAt = DateTime.UtcNow, CreatedById = project.CreatedById, IsActive = true, Order = 2, Color = "#FFC107" },
				new Priority { Name = "High", ProjectId = project.Id, ProjectKey = project.ProjectKey, CreatedAt = DateTime.UtcNow, CreatedById = project.CreatedById, IsActive = true, Order = 3, Color = "#F44336" }
			};
			foreach (var priority in defaultPriorities)
			{
				await _priorityRepository.CreateAsync(priority);
			}

			// Return ProjectResponse instead of Project entity
			var projectDto = new ProjectResponse
			{
				Id = project.Id,
				ProjectKey = project.ProjectKey,
				Name = project.Name,
				AvatarUrl = project.AvatarUrl,
				Last_Issue_Number = project.Last_Issue_Number,
				IsActive = project.IsActive,
				CreatedAt = project.CreatedAt,
				CreatedById = project.CreatedById,
				LastModifiedAt = project.LastModifiedAt,
				LastModifiedById = project.LastModifiedById,
				UserRole = "Owner",
				UserJoinedAt = DateTime.UtcNow,
				MemberCount = 1
			};

			return Result<ProjectResponse>.Success(projectDto);
		}

		public async Task<Result<IEnumerable<ProjectResponse>>> GetAllProjectByUserIdAsync(int userId)
		{
			var userProjects = await _userProjectRepository.GetByUserIdAsync(userId);

			var projectDtos = userProjects.Select(up => new ProjectResponse
			{
				Id = up.Project.Id,
				ProjectKey = up.Project.ProjectKey,
				Name = up.Project.Name,
				AvatarUrl = up.Project.AvatarUrl,
				Last_Issue_Number = up.Project.Last_Issue_Number,
				IsActive = up.Project.IsActive,
				CreatedAt = up.Project.CreatedAt,
				CreatedById = up.Project.CreatedById,
				LastModifiedAt = up.Project.LastModifiedAt,
				LastModifiedById = up.Project.LastModifiedById,
				UserRole = up.Role?.Name,
				UserJoinedAt = up.JoinedAt,
				MemberCount = up.Project.UserProjects?.Count ?? 0
			}).ToList();

			return Result<IEnumerable<ProjectResponse>>.Success(projectDtos);
		}

		public async Task<Result<bool>> AddUserToProjectAsync(int userId, int projectId, int? roleId = null)
		{
			// Validate project exists
			var project = await _projectRepository.GetByIdAsync(projectId);
			if (project == null)
				return Result<bool>.Failure(ErrorCode.NotFound, "Không tìm thấy dự án");

			// Validate user exists (if you have user repository)
			// var user = await _userRepository.GetByIdAsync(userId);
			// if (user == null)
			//     return Result<bool>.Failure(ErrorCode.NotFound, "Không tìm thấy người dùng");

			var success = await _userProjectRepository.AddUserToProjectAsync(userId, projectId, roleId);
			
			if (!success)
				return Result<bool>.Failure(ErrorCode.Conflict, "Người dùng đã tham gia dự án này");

			return Result<bool>.Success(true);
		}

		public async Task<Result<bool>> RemoveUserFromProjectAsync(int userId, int projectId)
		{
			var success = await _userProjectRepository.RemoveUserFromProjectAsync(userId, projectId);
			
			if (!success)
				return Result<bool>.Failure(ErrorCode.NotFound, "Không tìm thấy người dùng trong dự án");

			return Result<bool>.Success(true);
		}

		public async Task<Result<bool>> UpdateUserRoleInProjectAsync(int userId, int projectId, int? roleId)
		{
			var success = await _userProjectRepository.UpdateUserRoleInProjectAsync(userId, projectId, roleId);
			
			if (!success)
				return Result<bool>.Failure(ErrorCode.NotFound, "Không tìm thấy người dùng trong dự án");

			return Result<bool>.Success(true);
		}

		public async Task<Result<IEnumerable<ProjectMemberResponse>>> GetProjectMembersAsync(int projectId)
		{
			var userProjects = await _userProjectRepository.GetByProjectIdAsync(projectId);
			var members = userProjects.Select(up => new ProjectMemberResponse
			{
				UserId = up.UserId,
				UserName = up.User?.FullName ?? "",
				UserEmail = up.User?.Email ?? "",
				RoleName = up.Role?.Name,
				JoinedAt = up.JoinedAt,
				IsActive = up.IsActive
			}).ToList();

			return Result<IEnumerable<ProjectMemberResponse>>.Success(members);
		}

		public async Task<Result<ProjectDetailResponse>> GetByIdAsync(int id)
		{
			var project = await _projectRepository.GetByIdAsync(id);
			if (project == null)
				return Result<ProjectDetailResponse>.Failure(ErrorCode.NotFound, "Không tìm thấy dự án nào");

			// Get project members
			var userProjects = await _userProjectRepository.GetByProjectIdAsync(id);
			var roles = await _roleRepository.GetActiveByProjectIdAsync(id);

			var projectDto = new ProjectDetailResponse
			{
				Id = project.Id,
				ProjectKey = project.ProjectKey,
				Name = project.Name,
				AvatarUrl = project.AvatarUrl,
				Last_Issue_Number = project.Last_Issue_Number,
				IsActive = project.IsActive,
				CreatedAt = project.CreatedAt,
				CreatedById = project.CreatedById,
				LastModifiedAt = project.LastModifiedAt,
				LastModifiedById = project.LastModifiedById,
				MemberCount = userProjects.Count(),
				Members = userProjects.Select(up => new ProjectMemberResponse
				{
					UserId = up.UserId,
					UserName = up.User?.FullName ?? "",
					UserEmail = up.User?.Email ?? "",
					RoleName = up.Role?.Name,
					JoinedAt = up.JoinedAt,
					IsActive = up.IsActive
				}).ToList(),
				Roles = roles.Select(r => new RoleResponse
				{
					Id = r.Id,
					Name = r.Name,
					Description = r.Description,
					IsActive = r.IsActive
				}).ToList()
			};

			return Result<ProjectDetailResponse>.Success(projectDto);
		}

		

		public async Task<Result<bool>> UpdateAsync(int id, UpdateProjectRequest dto)
		{
			var project = await _projectRepository.GetByIdAsync(id);
			if (project == null)
				return Result<bool>.Failure(ErrorCode.NotFound, "Không tìm thấy dự án nào");

			// Update project properties
			project.Name = dto.Name ?? project.Name;
			project.ProjectKey = dto.ProjectKey ?? project.ProjectKey;
			project.IsActive = dto.IsActive;

			// Audit
			project.LastModifiedAt = DateTime.UtcNow;
			project.LastModifiedById = _currentUserService.GetUserId();

			await _projectRepository.UpdateAsync(project);

			return Result<bool>.Success(true);
		}

		public async Task<Result<bool>> SoftDeleteAsync(int id)
		{
			var project = await _projectRepository.GetByIdAsync(id);
			if (project == null)
				return Result<bool>.Failure(ErrorCode.NotFound, "Không tìm thấy dự án nào");

			project.IsDeleted = true;
			project.LastModifiedAt = DateTime.UtcNow;
			project.LastModifiedById = _currentUserService.GetUserId();

			await _projectRepository.UpdateAsync(project);

			return Result<bool>.Success(true);
		}
	}
}
