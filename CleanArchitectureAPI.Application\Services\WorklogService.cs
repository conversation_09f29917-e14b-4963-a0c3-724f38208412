using CleanArchitectureAPI.Application.DTOs;
using CleanArchitectureAPI.Application.Interfaces;
using CleanArchitectureAPI.Domain.Interfaces;
using CleanArchitectureAPI.Entities.Issue;

namespace CleanArchitectureAPI.Application.Services
{
    public class WorklogService : IWorklogService
    {
        private readonly IWorklogRepository _worklogRepository;
        private readonly ICurrentUserService _currentUserService;

        public WorklogService(
            IWorklogRepository worklogRepository,
            ICurrentUserService currentUserService)
        {
            _worklogRepository = worklogRepository;
            _currentUserService = currentUserService;
        }

        #region Basic CRUD Operations

        public async Task<WorklogDto> CreateWorklogAsync(CreateWorklogDto createWorklogDto)
        {
            var currentUserId = _currentUserService.GetUserId();
            
            var worklog = new Worklog
            {
                IssueId = createWorklogDto.IssueId,
                AuthorId = currentUserId,
                WorklogBody = createWorklogDto.WorklogBody,
                StartDate = createWorklogDto.StartDate,
                EndDate = createWorklogDto.EndDate,
                TimeWorkedSeconds = createWorklogDto.TimeWorkedSeconds,
                RemainingEstimateSeconds = createWorklogDto.RemainingEstimateSeconds,
                IsPublic = createWorklogDto.IsPublic,
                WorklogType = createWorklogDto.WorklogType,
                ProjectKey = "DEFAULT", // TODO: Get from context
                CreatedAt = DateTime.UtcNow,
                IsActive = true,
                Issue = null!, // Will be set by EF Core
                Author = null! // Will be set by EF Core
            };

            var createdWorklog = await _worklogRepository.CreateAsync(worklog);
            var worklogWithDetails = await _worklogRepository.GetByIdAsync(createdWorklog.Id);
            
            return MapToWorklogDto(worklogWithDetails!);
        }

        public async Task<WorklogDto?> GetWorklogByIdAsync(int id)
        {
            var worklog = await _worklogRepository.GetByIdAsync(id);
            return worklog != null ? MapToWorklogDto(worklog) : null;
        }

        public async Task<IEnumerable<WorklogDto>> GetWorklogsByIssueIdAsync(int issueId)
        {
            var worklogs = await _worklogRepository.GetByIssueIdAsync(issueId);
            return worklogs.Select(MapToWorklogDto);
        }

        public async Task<IEnumerable<WorklogDto>> GetWorklogsByAuthorIdAsync(int authorId)
        {
            var worklogs = await _worklogRepository.GetByAuthorIdAsync(authorId);
            return worklogs.Select(MapToWorklogDto);
        }

        public async Task<WorklogPagedResultDto> GetWorklogsPagedAsync(int issueId, int pageNumber, int pageSize)
        {
            var worklogs = await _worklogRepository.GetPagedAsync(issueId, pageNumber, pageSize);
            var totalCount = await _worklogRepository.GetCountByIssueIdAsync(issueId);

            return new WorklogPagedResultDto
            {
                Worklogs = worklogs.Select(MapToWorklogDto),
                TotalCount = totalCount,
                PageNumber = pageNumber,
                PageSize = pageSize
            };
        }

        public async Task<WorklogDto> UpdateWorklogAsync(int id, UpdateWorklogDto updateWorklogDto)
        {
            var worklog = await _worklogRepository.GetByIdAsync(id);
            if (worklog == null)
                throw new ArgumentException("Worklog not found");

            worklog.WorklogBody = updateWorklogDto.WorklogBody;
            worklog.StartDate = updateWorklogDto.StartDate;
            worklog.EndDate = updateWorklogDto.EndDate;
            worklog.TimeWorkedSeconds = updateWorklogDto.TimeWorkedSeconds;
            worklog.RemainingEstimateSeconds = updateWorklogDto.RemainingEstimateSeconds;
            worklog.IsPublic = updateWorklogDto.IsPublic;
            worklog.WorklogType = updateWorklogDto.WorklogType;
            worklog.LastModifiedAt = DateTime.UtcNow;
            worklog.LastModifiedById = _currentUserService.GetUserId();

            var updatedWorklog = await _worklogRepository.UpdateAsync(worklog);
            return MapToWorklogDto(updatedWorklog);
        }

        public async Task<bool> DeleteWorklogAsync(int id)
        {
            return await _worklogRepository.DeleteAsync(id);
        }

        #endregion

        #region Time Tracking

        public async Task<WorklogSummaryDto> GetWorklogSummaryAsync(int issueId)
        {
            var totalTimeWorked = await _worklogRepository.GetTotalTimeWorkedAsync(issueId);
            var worklogCount = await _worklogRepository.GetCountByIssueIdAsync(issueId);

            return new WorklogSummaryDto
            {
                IssueId = issueId,
                TotalTimeWorkedSeconds = totalTimeWorked,
                WorklogCount = worklogCount
            };
        }

        public async Task<int> GetTotalTimeWorkedAsync(int issueId)
        {
            return await _worklogRepository.GetTotalTimeWorkedAsync(issueId);
        }

        public async Task<int> GetTotalTimeWorkedByAuthorAsync(int issueId, int authorId)
        {
            return await _worklogRepository.GetTotalTimeWorkedByAuthorAsync(issueId, authorId);
        }

        public async Task<Dictionary<int, int>> GetTimeWorkedByIssuesAsync(IEnumerable<int> issueIds)
        {
            return await _worklogRepository.GetTimeWorkedByIssueAsync(issueIds);
        }

        #endregion

        #region Date Range Queries

        public async Task<IEnumerable<WorklogDto>> GetWorklogsByDateRangeAsync(int issueId, DateTime fromDate, DateTime toDate)
        {
            var worklogs = await _worklogRepository.GetByDateRangeAsync(issueId, fromDate, toDate);
            return worklogs.Select(MapToWorklogDto);
        }

        public async Task<IEnumerable<WorklogDto>> GetWorklogsByAuthorAndDateRangeAsync(int authorId, DateTime fromDate, DateTime toDate)
        {
            var worklogs = await _worklogRepository.GetByAuthorAndDateRangeAsync(authorId, fromDate, toDate);
            return worklogs.Select(MapToWorklogDto);
        }

        #endregion

        #region Statistics and Reporting

        public async Task<IEnumerable<WorklogDto>> GetRecentWorklogsAsync(int issueId, int count = 10)
        {
            var worklogs = await _worklogRepository.GetPagedAsync(issueId, 1, count);
            return worklogs.Select(MapToWorklogDto);
        }

        public async Task<Dictionary<string, object>> GetWorklogStatisticsAsync(int issueId)
        {
            var worklogs = await _worklogRepository.GetByIssueIdAsync(issueId);
            var worklogsList = worklogs.ToList();

            if (!worklogsList.Any())
            {
                return new Dictionary<string, object>
                {
                    ["totalTimeWorked"] = 0,
                    ["worklogCount"] = 0,
                    ["averageTimePerWorklog"] = 0,
                    ["firstWorklogDate"] = null,
                    ["lastWorklogDate"] = null,
                    ["uniqueAuthors"] = 0
                };
            }

            var totalTimeWorked = worklogsList.Sum(w => w.TimeWorkedSeconds);
            var worklogCount = worklogsList.Count;
            var averageTimePerWorklog = worklogCount > 0 ? totalTimeWorked / worklogCount : 0;
            var firstWorklogDate = worklogsList.Min(w => w.StartDate);
            var lastWorklogDate = worklogsList.Max(w => w.StartDate);
            var uniqueAuthors = worklogsList.Select(w => w.AuthorId).Distinct().Count();

            return new Dictionary<string, object>
            {
                ["totalTimeWorked"] = totalTimeWorked,
                ["worklogCount"] = worklogCount,
                ["averageTimePerWorklog"] = averageTimePerWorklog,
                ["firstWorklogDate"] = firstWorklogDate,
                ["lastWorklogDate"] = lastWorklogDate,
                ["uniqueAuthors"] = uniqueAuthors
            };
        }

        public async Task<Dictionary<string, object>> GetAuthorWorklogStatisticsAsync(int authorId, DateTime? fromDate = null, DateTime? toDate = null)
        {
            IEnumerable<Worklog> worklogs;
            
            if (fromDate.HasValue && toDate.HasValue)
            {
                worklogs = await _worklogRepository.GetByAuthorAndDateRangeAsync(authorId, fromDate.Value, toDate.Value);
            }
            else
            {
                worklogs = await _worklogRepository.GetByAuthorIdAsync(authorId);
            }

            var worklogsList = worklogs.ToList();

            if (!worklogsList.Any())
            {
                return new Dictionary<string, object>
                {
                    ["totalTimeWorked"] = 0,
                    ["worklogCount"] = 0,
                    ["averageTimePerWorklog"] = 0,
                    ["firstWorklogDate"] = null,
                    ["lastWorklogDate"] = null,
                    ["uniqueIssues"] = 0
                };
            }

            var totalTimeWorked = worklogsList.Sum(w => w.TimeWorkedSeconds);
            var worklogCount = worklogsList.Count;
            var averageTimePerWorklog = worklogCount > 0 ? totalTimeWorked / worklogCount : 0;
            var firstWorklogDate = worklogsList.Min(w => w.StartDate);
            var lastWorklogDate = worklogsList.Max(w => w.StartDate);
            var uniqueIssues = worklogsList.Select(w => w.IssueId).Distinct().Count();

            return new Dictionary<string, object>
            {
                ["totalTimeWorked"] = totalTimeWorked,
                ["worklogCount"] = worklogCount,
                ["averageTimePerWorklog"] = averageTimePerWorklog,
                ["firstWorklogDate"] = firstWorklogDate,
                ["lastWorklogDate"] = lastWorklogDate,
                ["uniqueIssues"] = uniqueIssues
            };
        }

        #endregion

        #region Validation

        public async Task<bool> CanUserEditWorklogAsync(int worklogId, int userId)
        {
            var worklog = await _worklogRepository.GetByIdAsync(worklogId);
            if (worklog == null) return false;

            // User can edit their own worklog
            return worklog.AuthorId == userId;
        }

        public async Task<bool> CanUserDeleteWorklogAsync(int worklogId, int userId)
        {
            var worklog = await _worklogRepository.GetByIdAsync(worklogId);
            if (worklog == null) return false;

            // User can delete their own worklog
            return worklog.AuthorId == userId;
        }

        public async Task<bool> IsWorklogValidAsync(CreateWorklogDto worklogDto)
        {
            // Basic validation
            if (worklogDto.TimeWorkedSeconds <= 0)
                return false;

            if (worklogDto.StartDate > DateTime.UtcNow)
                return false;

            if (worklogDto.EndDate.HasValue && worklogDto.EndDate < worklogDto.StartDate)
                return false;

            return true;
        }

        public async Task<bool> IsWorklogValidAsync(UpdateWorklogDto worklogDto)
        {
            // Basic validation
            if (worklogDto.TimeWorkedSeconds <= 0)
                return false;

            if (worklogDto.StartDate > DateTime.UtcNow)
                return false;

            if (worklogDto.EndDate.HasValue && worklogDto.EndDate < worklogDto.StartDate)
                return false;

            return true;
        }

        #endregion

        #region Mapping Methods

        private static WorklogDto MapToWorklogDto(Worklog worklog)
        {
            return new WorklogDto
            {
                Id = worklog.Id,
                IssueId = worklog.IssueId,
                AuthorId = worklog.AuthorId,
                AuthorName = worklog.Author != null ? worklog.Author.FullName : "Unknown",
                WorklogBody = worklog.WorklogBody,
                StartDate = worklog.StartDate,
                EndDate = worklog.EndDate,
                TimeWorkedSeconds = worklog.TimeWorkedSeconds,
                RemainingEstimateSeconds = worklog.RemainingEstimateSeconds,
                IsPublic = worklog.IsPublic,
                WorklogType = worklog.WorklogType,
                IsEdited = worklog.IsEdited,
                LastEditedAt = worklog.LastEditedAt,
                CreatedAt = worklog.CreatedAt,
                LastModifiedAt = worklog.LastModifiedAt
            };
        }

        #endregion
    }
}
