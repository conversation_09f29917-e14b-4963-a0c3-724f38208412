using FluentValidation;
using CleanArchitectureAPI.Application.DTOs.AppUser.Requests;

namespace CleanArchitectureAPI.Application.Validators.AppUser
{
    public class UpdateAppUserRequestValidator : AbstractValidator<UpdateAppUserRequest>
    {
        public UpdateAppUserRequestValidator()
        {
            RuleFor(x => x.FullName)
                .NotEmpty().WithMessage("Vui lòng nhập họ tên.")
                .MaximumLength(255).WithMessage("Họ tên không được vượt quá 255 ký tự.");

            RuleFor(x => x.Phone)
                .MaximumLength(20).WithMessage("Số điện thoại không được vượt quá 20 ký tự.")
                .When(x => !string.IsNullOrEmpty(x.Phone));

            RuleFor(x => x.Bio)
                .MaximumLength(500).WithMessage("Tiểu sử không được vượt quá 500 ký tự.")
                .When(x => !string.IsNullOrEmpty(x.Bio));

            RuleFor(x => x.Address)
                .MaximumLength(255).WithMessage("Địa chỉ không được vượt quá 255 ký tự.")
                .When(x => !string.IsNullOrEmpty(x.Address));
        }
    }
}
