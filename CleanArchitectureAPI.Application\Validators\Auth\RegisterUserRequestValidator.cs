﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CleanArchitectureAPI.Application.Requests.Auth;
using FluentValidation;

namespace CleanArchitectureAPI.Application.Validators.Auth
{
	public class RegisterUserRequestValidator : AbstractValidator<RegisterUserRequest>
	{
		public RegisterUserRequestValidator() {

			RuleFor(x => x.Email)
				.NotEmpty().WithMessage("Email không được để trống")
				.EmailAddress().WithMessage("Email không đúng định dạng")
				.MaximumLength(100).WithMessage("Email không được vượt quá 100 ký tự")
				.Matches(@"^[\w-.]+@([\w-]+\.)+[\w-]{2,4}$").WithMessage("Email không hợp lệ");

			RuleFor(x => x.Password)
				.NotEmpty().WithMessage("Mật khẩu không được để trống")
				.MinimumLength(6).WithMessage("Mật khẩu phải có ít nhất 6 ký tự");

			RuleFor(x => x.ConfirmPassword)
				.Equal(x => x.Password).WithMessage("Mật khẩu xác nhận không khớp");
		}
	}
}
