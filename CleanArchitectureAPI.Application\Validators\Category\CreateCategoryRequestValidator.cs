﻿using CleanArchitectureAPI.Application;
using FluentValidation;

namespace CleanArchitecture.API.Requests.Category
{
	public class CreateCategoryRequestValidator: AbstractValidator<CreateCategoryRequest>
	{
		public CreateCategoryRequestValidator() 
		{
			RuleFor(x => x.Name)
			.NotEmpty().WithMessage("Tên category không được trống")
			.MinimumLength(2).WithMessage("Tên category không nhỏ hơn 2 ký tự")
			.MaximumLength(100).WithMessage("Tên category tối đa 100 ký tự");

			RuleFor(x => x.Description)
			.MaximumLength(500).WithMessage("Mô tả tối đa 500 ký tự");
		}
	}
}
