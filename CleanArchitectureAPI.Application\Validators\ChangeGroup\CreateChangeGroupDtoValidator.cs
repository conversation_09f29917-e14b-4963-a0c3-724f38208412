using CleanArchitectureAPI.Application.DTOs;
using FluentValidation;

namespace CleanArchitectureAPI.Application.Validators.ChangeGroup
{
    public class CreateChangeGroupDtoValidator : AbstractValidator<CreateChangeGroupDto>
    {
        public CreateChangeGroupDtoValidator()
        {
            RuleFor(x => x.IssueId)
                .GreaterThan(0)
                .WithMessage("Issue ID must be greater than 0");

            RuleFor(x => x.Description)
                .MaximumLength(500)
                .WithMessage("Description cannot exceed 500 characters");

            RuleFor(x => x.ChangeType)
                .NotEmpty()
                .WithMessage("Change type is required")
                .Must(type => type == "Manual" || type == "Workflow" || type == "System")
                .WithMessage("Change type must be 'Manual', 'Workflow', or 'System'");

            RuleFor(x => x.ChangeItems)
                .NotNull()
                .WithMessage("Change items collection is required");

            RuleForEach(x => x.ChangeItems)
                .SetValidator(new CreateChangeItemDtoValidator());
        }
    }
}
