using CleanArchitectureAPI.Application.DTOs;
using FluentValidation;

namespace CleanArchitectureAPI.Application.Validators.ChangeGroup
{
    public class UpdateChangeGroupDtoValidator : AbstractValidator<UpdateChangeGroupDto>
    {
        public UpdateChangeGroupDtoValidator()
        {
            RuleFor(x => x.Description)
                .MaximumLength(500)
                .WithMessage("Description cannot exceed 500 characters");

            RuleFor(x => x.ChangeType)
                .NotEmpty()
                .WithMessage("Change type is required")
                .Must(type => type == "Manual" || type == "Workflow" || type == "System")
                .WithMessage("Change type must be 'Manual', 'Workflow', or 'System'");
        }
    }
}
