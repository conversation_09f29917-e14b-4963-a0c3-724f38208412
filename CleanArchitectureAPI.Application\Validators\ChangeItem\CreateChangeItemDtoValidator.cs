using CleanArchitectureAPI.Application.DTOs;
using FluentValidation;

namespace CleanArchitectureAPI.Application.Validators.ChangeGroup
{
    public class CreateChangeItemDtoValidator : AbstractValidator<CreateChangeItemDto>
    {
        public CreateChangeItemDtoValidator()
        {
            RuleFor(x => x.FieldType)
                .NotEmpty()
                .WithMessage("Field type is required")
                .MaximumLength(50)
                .WithMessage("Field type cannot exceed 50 characters")
                .Must(type => type == "jira" || type == "custom")
                .WithMessage("Field type must be 'jira' or 'custom'");

            RuleFor(x => x.Field)
                .NotEmpty()
                .WithMessage("Field name is required")
                .MaximumLength(255)
                .WithMessage("Field name cannot exceed 255 characters");

            RuleFor(x => x.FieldDisplayName)
                .MaximumLength(255)
                .WithMessage("Field display name cannot exceed 255 characters");

            RuleFor(x => x.DisplayOrder)
                .GreaterThanOrEqualTo(0)
                .WithMessage("Display order must be greater than or equal to 0");
        }
    }
}
