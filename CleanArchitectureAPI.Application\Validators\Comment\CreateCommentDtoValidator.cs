using CleanArchitectureAPI.Application.DTOs;
using FluentValidation;

namespace CleanArchitectureAPI.Application.Validators.Comment
{
    public class CreateCommentDtoValidator : AbstractValidator<CreateCommentDto>
    {
        public CreateCommentDtoValidator()
        {
            RuleFor(x => x.IssueId)
                .GreaterThan(0)
                .WithMessage("Issue ID must be greater than 0");

            RuleFor(x => x.ActionBody)
                .NotEmpty()
                .WithMessage("Comment body is required")
                .MaximumLength(10000)
                .WithMessage("Comment body cannot exceed 10000 characters");

            RuleFor(x => x.CommentType)
                .NotEmpty()
                .WithMessage("Comment type is required")
                .Must(type => type == "User" || type == "System")
                .WithMessage("Comment type must be either 'User' or 'System'");

            RuleFor(x => x.ChangeGroupId)
                .GreaterThan(0)
                .When(x => x.ChangeGroupId.HasValue)
                .WithMessage("Change Group ID must be greater than 0 when provided");
        }
    }
}
