using CleanArchitectureAPI.Application.DTOs;
using FluentValidation;

namespace CleanArchitectureAPI.Application.Validators.Comment
{
    public class UpdateCommentDtoValidator : AbstractValidator<UpdateCommentDto>
    {
        public UpdateCommentDtoValidator()
        {
            RuleFor(x => x.ActionBody)
                .NotEmpty()
                .WithMessage("Comment body is required")
                .MaximumLength(10000)
                .WithMessage("Comment body cannot exceed 10000 characters");
        }
    }
}
