﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using FluentValidation;

namespace CleanArchitectureAPI.Application.Validators
{
	public class CreateProjectRequestValidator : AbstractValidator<CreateProjectRequest>
	{
		public CreateProjectRequestValidator()
		{
			RuleFor(x => x.ProjectKey)
			.NotEmpty().WithMessage("Vui lòng nhập mã dự án.")
			.MaximumLength(50).WithMessage("Mã dự án không được vượt quá 50 ký tự.");

			RuleFor(x => x.Name)
				.NotEmpty().WithMessage("Vui lòng nhập tên dự án.")
				.MaximumLength(100).WithMessage("Tên dự án không được vượt quá 100 ký tự.");

		}
	}

}
