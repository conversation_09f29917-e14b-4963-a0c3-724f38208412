﻿using FluentValidation;
using CleanArchitecture.API.Requests;

namespace CleanArchitectureAPI.Application.Validators.IssueType
{
	public class CreateIssueTypeRequestValidator : AbstractValidator<CreateIssueTypeRequest>
	{
		public CreateIssueTypeRequestValidator()
		{
			RuleFor(x => x.<PERSON>ey)
				.NotEmpty().WithMessage("Vui lòng nhập mã dự án.")
				.MaximumLength(50).WithMessage("Mã dự án không được vượt quá 50 ký tự.");

			RuleFor(x => x.Name)
				.NotEmpty().WithMessage("Vui lòng nhập tên loại vấn đề.")
				.MaximumLength(100).WithMessage("Tên loại vấn đề không được vượt quá 100 ký tự.");

			RuleFor(x => x.Description)
				.MaximumLength(500).WithMessage("Mô tả tối đa 500 ký tự");

			RuleFor(x => x.Color)
				.NotEmpty().WithMessage("<PERSON>àu sắc không được để trống")
				.Matches("^#(?:[0-9a-fA-F]{3}){1,2}$").WithMessage("Màu sắc nên là mã HEX (Ví dụ: #FF0000).");
		}
	}
}
