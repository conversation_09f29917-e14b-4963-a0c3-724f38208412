﻿using FluentValidation;
using CleanArchitecture.API.Requests.IssueType;

namespace CleanArchitectureAPI.Application.Validators.IssueType
{
	public class UpdateIssueTypeRequestValidator : AbstractValidator<UpdateIssueTypeRequest>
	{
		public UpdateIssueTypeRequestValidator()
		{
			RuleFor(x => x.Project<PERSON>ey)
				.NotEmpty().WithMessage("Vui lòng nhập mã dự án.")
				.MaximumLength(50).WithMessage("Mã dự án không được vượt quá 50 ký tự.");

			RuleFor(x => x.Name)
				.NotEmpty().WithMessage("Vui lòng nhập tên loại vấn đề.")
				.MaximumLength(100).WithMessage("Tên loại vấn đề không được vượt quá 100 ký tự.");

			RuleFor(x => x.Description)
				.MaximumLength(500).WithMessage("<PERSON><PERSON> tả tối đa 500 ký tự");

			RuleFor(x => x.Color)
				.NotEmpty().WithMessage("<PERSON>àu sắc không được để trống")
				.Matches("^#(?:[0-9a-fA-F]{3}){1,2}$").WithMessage("Màu sắc nên là mã HEX (Ví dụ: #FF0000).");
		}
	}
}
