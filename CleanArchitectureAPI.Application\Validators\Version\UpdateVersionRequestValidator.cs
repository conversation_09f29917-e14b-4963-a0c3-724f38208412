using FluentValidation;
using CleanArchitecture.API.Requests.Version;

namespace CleanArchitectureAPI.Application.Validators.Version
{
	public class UpdateVersionRequestValidator : AbstractValidator<UpdateVersionRequest>
	{
		public UpdateVersionRequestValidator()
		{
			RuleFor(x => x.Project<PERSON>ey)
				.NotEmpty().WithMessage("Vui lòng nhập mã dự án.")
				.MaximumLength(50).WithMessage("Mã dự án không được vượt quá 50 ký tự.");

			RuleFor(x => x.Name)
				.NotEmpty().WithMessage("<PERSON>ui lòng nhập tên phiên bản.")
				.MaximumLength(100).WithMessage("Tên phiên bản không được vượt quá 100 ký tự.");

			RuleFor(x => x.Description)
				.MaximumLength(500).WithMessage("<PERSON>ô tả tối đa 500 ký tự");

			RuleFor(x => x.Color)
				.NotEmpty().WithMessage("<PERSON>àu sắc không được để trống")
				.Matches("^#(?:[0-9a-fA-F]{3}){1,2}$").WithMessage("Màu sắc nên là mã HEX (Ví dụ: #FF0000).");

			RuleFor(x => x.EndDate)
				.GreaterThan(x => x.StartDate)
				.When(x => x.StartDate.HasValue && x.EndDate.HasValue)
				.WithMessage("Ngày kết thúc phải sau ngày bắt đầu.");
		}
	}
}
