using CleanArchitectureAPI.Application.DTOs;
using FluentValidation;

namespace CleanArchitectureAPI.Application.Validators.Worklog
{
    public class UpdateWorklogDtoValidator : AbstractValidator<UpdateWorklogDto>
    {
        public UpdateWorklogDtoValidator()
        {
            RuleFor(x => x.WorklogBody)
                .MaximumLength(10000)
                .WithMessage("Worklog body cannot exceed 10000 characters");

            RuleFor(x => x.StartDate)
                .NotEmpty()
                .WithMessage("Start date is required")
                .LessThanOrEqualTo(DateTime.UtcNow)
                .WithMessage("Start date cannot be in the future");

            RuleFor(x => x.EndDate)
                .GreaterThanOrEqualTo(x => x.StartDate)
                .When(x => x.EndDate.HasValue)
                .WithMessage("End date must be greater than or equal to start date");

            RuleFor(x => x.TimeWorkedSeconds)
                .GreaterThan(0)
                .WithMessage("Time worked must be greater than 0 seconds")
                .LessThanOrEqualTo(86400 * 7) // 7 days in seconds
                .WithMessage("Time worked cannot exceed 7 days");

            RuleFor(x => x.RemainingEstimateSeconds)
                .GreaterThanOrEqualTo(0)
                .When(x => x.RemainingEstimateSeconds.HasValue)
                .WithMessage("Remaining estimate must be greater than or equal to 0");

            RuleFor(x => x.WorklogType)
                .NotEmpty()
                .WithMessage("Worklog type is required")
                .Must(type => type == "Manual" || type == "Automatic")
                .WithMessage("Worklog type must be 'Manual' or 'Automatic'");

            // Custom validation: if EndDate is provided, TimeWorkedSeconds should match the duration
            RuleFor(x => x)
                .Must(x => !x.EndDate.HasValue || 
                          Math.Abs((x.EndDate.Value - x.StartDate).TotalSeconds - x.TimeWorkedSeconds) <= 60)
                .When(x => x.EndDate.HasValue)
                .WithMessage("Time worked should approximately match the duration between start and end dates");
        }
    }
}
