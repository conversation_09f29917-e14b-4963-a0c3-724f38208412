﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json.Serialization;
using CleanArchitectureAPI.Domain.Entities.User;
using CleanArchitectureAPI.Domain.Enums;
using CleanArchitectureAPI.Domain.Common;

namespace CleanArchitectureAPI.Domain.Entities.User
{
	public class AppUser : BaseAudit
	{
		public string FullName { get; set; } = string.Empty;
		public string Email { get; set; } = string.Empty;
		public string PasswordHash { get; set; } = string.Empty;
        public UserStatus Status { get; set; } = UserStatus.Online; 
		public string? Avatar { get; set; }
		public string? Phone { get; set; }
		public string? Bio { get; set; }
		public string? Address { get; set; }
		public DateTime LastActive { get; set; } = DateTime.UtcNow;
		public DateTime LastLoginTime { get; set; } = DateTime.UtcNow;
		public bool HasCompletedInitialSetup { get; set; } = false;

		// Navigation properties
		[JsonIgnore]
		public ICollection<UserProject> UserProjects { get; set; } = new List<UserProject>();

		[JsonIgnore]
		public ICollection<UserRole> UserRoles { get; set; } = new List<UserRole>();

		[JsonIgnore]
		public ICollection<UserGroup> UserGroups { get; set; } = new List<UserGroup>();

		[JsonIgnore]
		public ICollection<UserPermission> UserPermissions { get; set; } = new List<UserPermission>();

        //public AppUser(string _firstName, string _lastName, string _mailAdress, string _passwordHash) {
        //	MailAddress = _mailAdress;
        //	PasswordHash = _passwordHash;
        //	FirstName = _firstName;
        //	LastName = _lastName;

        //}
    }
}
