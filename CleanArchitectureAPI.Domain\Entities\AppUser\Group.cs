﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CleanArchitectureAPI.Domain.Common;

namespace CleanArchitectureAPI.Domain.Entities.User
{
	public class Group : BaseProject
    {
		[Required]
		[Column(TypeName = "nvarchar(100)")]
		public string Name { get; set; } = string.Empty;

		[Column(TypeName = "nvarchar(255)")]
		public string? Description { get; set; }
	}
}
