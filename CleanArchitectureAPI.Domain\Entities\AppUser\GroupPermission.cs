﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CleanArchitectureAPI.Domain.Common;

namespace CleanArchitectureAPI.Domain.Entities.User
{
	public class GroupPermission : BaseAudit
    {
		[Required]
		public int GroupId { get; set; }

		[Required]
		public int PermissionId { get; set; }

		[ForeignKey(nameof(GroupId))]
		public Group Group { get; set; } = null!;

		[ForeignKey(nameof(PermissionId))]
		public Permission Permission { get; set; } = null!;
	}
}
