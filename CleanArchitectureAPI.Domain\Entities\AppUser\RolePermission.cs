﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CleanArchitectureAPI.Domain.Entities.User;
using CleanArchitectureAPI.Domain.Common;

namespace CleanArchitectureAPI.Domain.Entities.User
{
    public class RolePermission: BaseAudit
    {
        public int RoleId { get; set; }
        public required Role Role { get; set; }
        public int PermissionId { get; set; }
        public required Permission Permission { get; set; }
    }
}
