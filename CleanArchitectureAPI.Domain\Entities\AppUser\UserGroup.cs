﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CleanArchitectureAPI.Domain.Common;
using CleanArchitectureAPI.Domain.Entities.User;

namespace CleanArchitectureAPI.Domain.Entities.User
{
	public class UserGroup : BaseAudit
	{
		[Required]
		public int UserId { get; set; }

		[Required]
		public int GroupId { get; set; }


		[ForeignKey(nameof(UserId))]
		public AppUser User { get; set; } = null!;

		[ForeignKey(nameof(GroupId))]

		public Group Group { get; set; } = null!;
	}
}
