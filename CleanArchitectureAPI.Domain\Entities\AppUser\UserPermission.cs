﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CleanArchitectureAPI.Domain.Common;
using CleanArchitectureAPI.Domain.Entities.User;

namespace CleanArchitectureAPI.Domain.Entities.User
{
	public class UserPermission : BaseProject
	{
		[Required]
		public int UserId { get; set; }

		[Required]
		public int PermissionId { get; set; }
		
		[ForeignKey(nameof(UserId))]
		public AppUser User { get; set; } = null!;

		[ForeignKey(nameof(PermissionId))]
		public Permission Permission { get; set; } = null!;
	}
}
