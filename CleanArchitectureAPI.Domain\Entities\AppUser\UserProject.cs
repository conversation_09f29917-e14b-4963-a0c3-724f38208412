using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json.Serialization;
using CleanArchitectureAPI.Domain.Common;
using CleanArchitectureAPI.Domain.Entities.User;
using CleanArchitectureAPI.Domain.Entities;

namespace CleanArchitectureAPI.Domain.Entities.User
{
    public class UserProject : BaseAudit
    {
        [Required]
        public int UserId { get; set; }

        [Required]
        public int ProjectId { get; set; }

        [ForeignKey(nameof(UserId))]
        [JsonIgnore]
        public AppUser User { get; set; } = null!;

        [ForeignKey(nameof(ProjectId))]
        [JsonIgnore]
        public Project Project { get; set; } = null!;

        // Vai trò của user trong project này (có thể null nếu chỉ là member)
        public int? RoleId { get; set; }

        [ForeignKey(nameof(RoleId))]
        [JsonIgnore]
        public Role? Role { get; set; }

        // Ng<PERSON>y tham gia project
        public DateTime JoinedAt { get; set; } = DateTime.UtcNow;

        // Trạng thái trong project
        public new bool IsActive { get; set; } = true;
    }
}