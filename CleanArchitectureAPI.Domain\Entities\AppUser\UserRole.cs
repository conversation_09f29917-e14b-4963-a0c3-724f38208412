﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CleanArchitectureAPI.Domain.Common;

namespace CleanArchitectureAPI.Domain.Entities.User
{
    public class UserRole : BaseAudit
    {
        public int UserId { get; set; }
        public required AppUser User { get; set; } = null!;
        public int RoleId { get; set; }
        public required Role Role { get; set; }
    }
}
