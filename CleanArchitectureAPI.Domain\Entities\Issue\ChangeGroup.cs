﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CleanArchitectureAPI.Domain.Common;
using CleanArchitectureAPI.Domain.Entities.User;

namespace CleanArchitectureAPI.Entities.Issue
{
    public class ChangeGroup : BaseProject
    {
        public int IssueId { get; set; }

        [ForeignKey(nameof(IssueId))]
        public required Issue Issue { get; set; }

        public int AuthorId { get; set; }

        [ForeignKey(nameof(AuthorId))]
        public required AppUser Author { get; set; }

        /// <summary>
        /// Mô tả ngắn gọn về nhóm thay đổi này
        /// </summary>
        [Column(TypeName = "nvarchar(500)")]
        public string? Description { get; set; }

        /// <summary>
        /// Loại thay đổi: Manual (thay đổi thủ công), Workflow (thay đổi theo workflow), System (thay đổi tự động)
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string ChangeType { get; set; } = "Manual";

        /// <summary>
        /// Danh sách các thay đổi chi tiết trong nhóm này
        /// </summary>
        public ICollection<ChangeItem> ChangeItems { get; set; } = new List<ChangeItem>();

        /// <summary>
        /// Danh sách các comment liên quan đến nhóm thay đổi này
        /// </summary>
        public ICollection<Comment> Comments { get; set; } = new List<Comment>();
    }
}
