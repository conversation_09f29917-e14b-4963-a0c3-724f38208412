﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CleanArchitectureAPI.Domain.Common;

namespace CleanArchitectureAPI.Entities.Issue
{
    public class ChangeItem : BaseProject
    {
        public int GroupId { get; set; }

        [ForeignKey(nameof(GroupId))]
        public required ChangeGroup ChangeGroup { get; set; }

        /// <summary>
        /// Loại trường: jira (trường chuẩn), custom (trường tùy chỉnh)
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public required string FieldType { get; set; }

        /// <summary>
        /// Tên trường được thay đổi (ví dụ: status, assignee, description)
        /// </summary>
        [Column(TypeName = "varchar(255)")]
        public required string Field { get; set; }

        /// <summary>
        /// Tên hiển thị của trường (ví dụ: "Trạng thái", "Người được giao")
        /// </summary>
        [Column(TypeName = "nvarchar(255)")]
        public string? FieldDisplayName { get; set; }

        /// <summary>
        /// Giá trị cũ (ID hoặc giá trị thực)
        /// </summary>
        [Column(TypeName = "nvarchar(max)")]
        public string? OldValue { get; set; }

        /// <summary>
        /// Chuỗi hiển thị của giá trị cũ
        /// </summary>
        [Column(TypeName = "nvarchar(max)")]
        public string? OldString { get; set; }

        /// <summary>
        /// Giá trị mới (ID hoặc giá trị thực)
        /// </summary>
        [Column(TypeName = "nvarchar(max)")]
        public string? NewValue { get; set; }

        /// <summary>
        /// Chuỗi hiển thị của giá trị mới
        /// </summary>
        [Column(TypeName = "nvarchar(max)")]
        public string? NewString { get; set; }

        /// <summary>
        /// Thứ tự hiển thị trong nhóm thay đổi
        /// </summary>
        public int DisplayOrder { get; set; } = 0;
    }
}
