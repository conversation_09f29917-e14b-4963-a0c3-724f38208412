﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CleanArchitectureAPI.Domain.Common;
using CleanArchitectureAPI.Domain.Entities.User;

namespace CleanArchitectureAPI.Entities.Issue
{
    public class Comment : BaseProject
    {
        public int IssueId { get; set; }

        [ForeignKey(nameof(IssueId))]
        public required Issue Issue { get; set; }

        public int AuthorId { get; set; }

        [ForeignKey(nameof(AuthorId))]
        public required AppUser Author { get; set; }

        [Column(TypeName = "nvarchar(max)")]
        public required string ActionBody { get; set; }

        /// <summary>
        /// Loại comment: User (comment của user), System (comment tự động từ hệ thống)
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string CommentType { get; set; } = "User";

        /// <summary>
        /// ID của ChangeGroup nếu comment này liên quan đến một thay đổi
        /// </summary>
        public int? ChangeGroupId { get; set; }

        [ForeignKey(nameof(ChangeGroupId))]
        public ChangeGroup? ChangeGroup { get; set; }

        /// <summary>
        /// Có phải comment được chỉnh sửa không
        /// </summary>
        public bool IsEdited { get; set; } = false;

        /// <summary>
        /// Thời gian chỉnh sửa lần cuối
        /// </summary>
        public DateTime? LastEditedAt { get; set; }
   }
}
