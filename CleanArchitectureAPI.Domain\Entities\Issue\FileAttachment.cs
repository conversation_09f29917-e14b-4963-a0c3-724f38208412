﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CleanArchitectureAPI.Domain.Common;
using CleanArchitectureAPI.Domain.Entities.User;

namespace CleanArchitectureAPI.Entities.Issue
{
    public class FileAttachment : BaseProject
    {
        public int IssueId { get; set; }

        [ForeignKey(nameof(IssueId))]
        public required Issue Issue { get; set; }

        [Column(TypeName = "varchar(255)")]
        public required string FileName { get; set; }
        public long FileSize { get; set; }
        public int AuthorId { get; set; }

        [ForeignKey(nameof(AuthorId))]
        public required AppUser Author { get; set; }

        [Column(TypeName = "varchar(255)")]
        public required string FileType { get; set; }

        [Column(TypeName = "varchar(max)")]
        public required string FileUrl { get; set; }
    }
}
