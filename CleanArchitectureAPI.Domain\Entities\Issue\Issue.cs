﻿
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.VisualBasic;
using CleanArchitectureAPI.Domain.Common;
using CleanArchitectureAPI.Domain.Entities.User;
using CleanArchitectureAPI.Domain.Entities;

namespace CleanArchitectureAPI.Entities.Issue
{
    public class Issue :  BaseProject
    {
        public required string ProjectIssueKey { get; set; }

        public required string Subject { get; set; }
        public string? Description { get; set; }

        public int? AssigneeId { get; set; }

        [ForeignKey(nameof(AssigneeId))]

        public AppUser? Assignee { get; set; }

        public required int ReporterId { get; set; }

        [ForeignKey(nameof(ReporterId))]
        public AppUser? Reporter { get; set; }

        public DateTime? DueDate { get; set; }

        public required int IssueTypeId { get; set; }

        [ForeignKey(nameof(IssueTypeId))]
        public IssueType? IssueType { get; set; }

        public required int StatusId { get; set; }

        [ForeignKey(nameof(StatusId))]
        public Status? Status { get; set; }

        public required int PriorityId { get; set; }

        [ForeignKey(nameof(PriorityId))]
        public Priority? Priority { get; set; }

        public ICollection<IssueCategory>? IssueCategories { get; set; }

        public ICollection<IssueMilestone>? IssueMilestones { get; set; }

        public ICollection<IssueVersion>? IssueVersions { get; set; }

        public ICollection<Comment>? Comments { get; set; }

        public ICollection<ChangeGroup>? ChangeGroups { get; set; }

        public ICollection<Worklog>? Worklogs { get; set; }
    }
}
