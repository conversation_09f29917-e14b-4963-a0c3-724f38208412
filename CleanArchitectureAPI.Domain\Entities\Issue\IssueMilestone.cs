﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CleanArchitectureAPI.Domain.Common;
using CleanArchitectureAPI.Domain.Entities;

namespace CleanArchitectureAPI.Entities.Issue
{
    public class IssueMilestone : BaseProject
    {
        public int IssueId { get; set; }
        [ForeignKey(nameof(IssueId))]
        public Issue? Issue { get; set; }

        public int MilestoneId { get; set; }
        [ForeignKey(nameof(MilestoneId))]
        public Milestone? Milestone { get; set; }
    }
}
