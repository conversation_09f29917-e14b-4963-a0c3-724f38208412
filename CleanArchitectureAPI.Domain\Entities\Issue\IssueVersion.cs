﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CleanArchitectureAPI.Domain.Common;

namespace CleanArchitectureAPI.Entities.Issue
{
    public class IssueVersion : BaseProject
    {
        public int IssueId { get; set; }
        [ForeignKey(nameof(IssueId))]
        public Issue? Issue { get; set; }

        public int VersionId { get; set; }
        [ForeignKey(nameof(VersionId))]
        public Domain.Entities.Version? Version { get; set; }
    }
}
