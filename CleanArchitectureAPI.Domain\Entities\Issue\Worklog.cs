﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CleanArchitectureAPI.Domain.Common;
using CleanArchitectureAPI.Domain.Entities.User;

namespace CleanArchitectureAPI.Entities.Issue
{
    public class Worklog : BaseProject
    {
        public int IssueId { get; set; }

        [ForeignKey(nameof(IssueId))]
        public required Issue Issue { get; set; }

        public int AuthorId { get; set; }

        [ForeignKey(nameof(AuthorId))]
        public required AppUser Author { get; set; }

        /// <summary>
        /// Mô tả công việc đã thực hiện
        /// </summary>
        [Column(TypeName = "nvarchar(max)")]
        public string? WorklogBody { get; set; }

        /// <summary>
        /// Thời gian bắt đầu làm việc
        /// </summary>
        [Required]
        public DateTime StartDate { get; set; }

        /// <summary>
        /// Thời gian kết thúc làm việc (có thể null nếu chỉ log thời gian làm việc)
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// Thời gian làm việc tính bằng giây
        /// </summary>
        [Required]
        public int TimeWorkedSeconds { get; set; }

        /// <summary>
        /// Thời gian còn lại ước tính tính bằng giây (có thể null)
        /// </summary>
        public int? RemainingEstimateSeconds { get; set; }

        /// <summary>
        /// Có hiển thị công khai hay không
        /// </summary>
        public bool IsPublic { get; set; } = true;

        /// <summary>
        /// Loại worklog: Manual (thủ công), Automatic (tự động)
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string WorklogType { get; set; } = "Manual";

        /// <summary>
        /// Có được chỉnh sửa không
        /// </summary>
        public bool IsEdited { get; set; } = false;

        /// <summary>
        /// Thời gian chỉnh sửa lần cuối
        /// </summary>
        public DateTime? LastEditedAt { get; set; }
    }
}
