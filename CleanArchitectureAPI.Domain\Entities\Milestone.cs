﻿using CleanArchitectureAPI.Entities.Issue;
using CleanArchitectureAPI.Domain.Common;

namespace CleanArchitectureAPI.Domain.Entities
{
	public class Milestone : BaseProjectSetting
    {
		public DateTime? StartDate { get; set; } = null;
		public DateTime? EndDate { get; set; } = null;
		public bool IsReleased { get; set; } = false;
        public ICollection<IssueMilestone> IssueMilestones { get; set; } = new List<IssueMilestone>();
    }
}
