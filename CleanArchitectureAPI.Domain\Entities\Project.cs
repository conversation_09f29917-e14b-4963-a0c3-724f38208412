﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json.Serialization;
using CleanArchitectureAPI.Domain.Entities.User;
using CleanArchitectureAPI.Domain.Common;

namespace CleanArchitectureAPI.Domain.Entities
{
	public class Project : BaseAudit
	{
		public string ProjectKey { get; set; } = string.Empty;
		public string Name { get; set; } = string.Empty;
		public string? AvatarUrl { get; set; }
		public int Last_Issue_Number { get; set; } = 1;

		// Navigation properties
		[JsonIgnore]
		public ICollection<Role> Roles { get; set; } = new List<Role>();

		[JsonIgnore]
		public ICollection<UserProject> UserProjects { get; set; } = new List<UserProject>();
	}
}
