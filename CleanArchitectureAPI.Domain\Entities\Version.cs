﻿using CleanArchitectureAPI.Entities.Issue;
using CleanArchitectureAPI.Domain.Common;

namespace CleanArchitectureAPI.Domain.Entities
{
	public class Version : BaseProjectSetting
    {
		public DateTime? StartDate { get; set; } = null;
		public DateTime? EndDate { get; set; } = null;
		public bool IsReleased { get; set; } = false;
        public ICollection<IssueVersion> IssueVersions { get; set; } = new List<IssueVersion>();
    }
}
