﻿using CleanArchitectureAPI.Domain.Entities.User;
using CleanArchitectureAPI.Domain.Interfaces.Repositories;

namespace CleanArchitectureAPI.Domain.Interfaces.User
{
    public interface IAppUserRepository : IRepository<AppUser>
    {
        AppUser Authenticate(string userName, string password);
        Task<bool> IsEmailExistsAsync(string email);
        Task<AppUser?> GetByEmailAsync(string email);
        Task<IEnumerable<AppUser>> GetAllActiveAsync();
        Task<bool> IsEmailExistsAsync(string email, int excludeUserId);
        Task<IEnumerable<AppUser>> GetUsersByProjectKeyAsync(string projectKey);
        Task<IEnumerable<AppUser>> GetAllActiveByProjectKeyAsync(string projectKey);
	}
}
