﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CleanArchitectureAPI.Domain.Entities.User;
using CleanArchitectureAPI.Domain.Interfaces.Repositories;

namespace CleanArchitectureAPI.Domain.Interfaces
{
    public interface IAuthRepository : IRepository<AppUser>
    {
        AppUser Authenticate(string userName, string password);
        Task<bool> IsEmailExistsAsync(string email);
        Task<AppUser> GetUserByEmailAsync(string email);
	}
}
