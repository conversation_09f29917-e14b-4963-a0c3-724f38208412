﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CleanArchitectureAPI.Domain.Entities;
using CleanArchitectureAPI.Domain.Interfaces.Repositories;

namespace CleanArchitectureAPI.Domain.Interfaces
{
	public interface ICategoryRepository : IRepository<Category>
	{
		Task<bool> IsNameDuplicated(string projectKey, string name);
		Task<int> GetMaxOrderInProjectAsync(string projectKey);

		// Methods for business operations (only active items)
		Task<IEnumerable<Category>> GetAllActiveByProjectKeyAsync(string projectKey);

		// Methods for admin/history (include inactive items)
		Task<IEnumerable<Category>> GetAllByProjectKeyAsync(string projectKey);

		Task ReorderCategoriesAsync(string projectKey, List<int> categoryIdsInOrder);
	}
}
