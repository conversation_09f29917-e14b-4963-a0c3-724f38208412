using CleanArchitectureAPI.Entities.Issue;

namespace CleanArchitectureAPI.Domain.Interfaces
{
    public interface IChangeGroupRepository
    {
        Task<ChangeGroup> CreateAsync(ChangeGroup changeGroup);
        Task<ChangeGroup?> GetByIdAsync(int id);
        Task<ChangeGroup?> GetByIdWithItemsAsync(int id);
        Task<IEnumerable<ChangeGroup>> GetByIssueIdAsync(int issueId);
        Task<IEnumerable<ChangeGroup>> GetByIssueIdWithItemsAsync(int issueId);
        Task<ChangeGroup> UpdateAsync(ChangeGroup changeGroup);
        Task<bool> DeleteAsync(int id);
        Task<bool> ExistsAsync(int id);
        Task<IEnumerable<ChangeGroup>> GetPagedAsync(int issueId, int pageNumber, int pageSize);
        Task<int> GetCountByIssueIdAsync(int issueId);
        Task<IEnumerable<ChangeGroup>> GetByAuthorIdAsync(int authorId);
        Task<IEnumerable<ChangeGroup>> GetByDateRangeAsync(int issueId, DateTime fromDate, DateTime toDate);
    }
}
