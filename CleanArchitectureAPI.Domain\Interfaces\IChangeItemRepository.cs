using CleanArchitectureAPI.Entities.Issue;

namespace CleanArchitectureAPI.Domain.Interfaces
{
    public interface IChangeItemRepository
    {
        Task<ChangeItem> CreateAsync(ChangeItem changeItem);
        Task<ChangeItem?> GetByIdAsync(int id);
        Task<IEnumerable<ChangeItem>> GetByGroupIdAsync(int groupId);
        Task<ChangeItem> UpdateAsync(ChangeItem changeItem);
        Task<bool> DeleteAsync(int id);
        Task<bool> ExistsAsync(int id);
        Task<IEnumerable<ChangeItem>> GetByFieldAsync(int issueId, string field);
        Task<IEnumerable<ChangeItem>> GetByFieldTypeAsync(int issueId, string fieldType);
        Task<bool> DeleteByGroupIdAsync(int groupId);
        Task<IEnumerable<ChangeItem>> CreateBulkAsync(IEnumerable<ChangeItem> changeItems);
    }
}
