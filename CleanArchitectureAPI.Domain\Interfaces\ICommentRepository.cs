using CleanArchitectureAPI.Entities.Issue;

namespace CleanArchitectureAPI.Domain.Interfaces
{
    public interface ICommentRepository
    {
        Task<Comment> CreateAsync(Comment comment);
        Task<Comment?> GetByIdAsync(int id);
        Task<IEnumerable<Comment>> GetByIssueIdAsync(int issueId);
        Task<IEnumerable<Comment>> GetByChangeGroupIdAsync(int changeGroupId);
        Task<Comment> UpdateAsync(Comment comment);
        Task<bool> DeleteAsync(int id);
        Task<bool> ExistsAsync(int id);
        Task<IEnumerable<Comment>> GetPagedAsync(int issueId, int pageNumber, int pageSize);
        Task<int> GetCountByIssueIdAsync(int issueId);
    }
}
