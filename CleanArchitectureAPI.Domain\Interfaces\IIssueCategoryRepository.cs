using CleanArchitectureAPI.Domain.Interfaces.Repositories;
using CleanArchitectureAPI.Entities.Issue;

namespace CleanArchitectureAPI.Domain.Interfaces
{
    public interface IIssueCategoryRepository : IRepository<IssueCategory>
    {
        Task<IEnumerable<IssueCategory>> GetByIssueIdAsync(int issueId);
        Task<IEnumerable<IssueCategory>> GetByCategoryIdAsync(int categoryId);
        Task<bool> DeleteByIssueIdAsync(int issueId);
        Task<bool> DeleteByCategoryIdAsync(int categoryId);
        Task<bool> ExistsAsync(int issueId, int categoryId);
    }
}
