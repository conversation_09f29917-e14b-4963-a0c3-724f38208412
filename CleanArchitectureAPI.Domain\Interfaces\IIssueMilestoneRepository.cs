using CleanArchitectureAPI.Domain.Interfaces.Repositories;
using CleanArchitectureAPI.Entities.Issue;

namespace CleanArchitectureAPI.Domain.Interfaces
{
    public interface IIssueMilestoneRepository : IRepository<IssueMilestone>
    {
        Task<IEnumerable<IssueMilestone>> GetByIssueIdAsync(int issueId);
        Task<IEnumerable<IssueMilestone>> GetByMilestoneIdAsync(int milestoneId);
        Task<bool> DeleteByIssueIdAsync(int issueId);
        Task<bool> DeleteByMilestoneIdAsync(int milestoneId);
        Task<bool> ExistsAsync(int issueId, int milestoneId);
    }
}
