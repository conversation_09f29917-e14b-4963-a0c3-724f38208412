using CleanArchitectureAPI.Domain.Common;
using CleanArchitectureAPI.Domain.Interfaces.Repositories;
using CleanArchitectureAPI.Entities.Issue;

namespace CleanArchitectureAPI.Domain.Interfaces
{
    public interface IIssueRepository : IRepository<Issue>
    {
        // Get by projectIssueKey
        Task<Issue?> GetByProjectIssueKeyAsync(string projectIssueKey);

        // Get by project
        Task<IEnumerable<Issue>> GetAllByProjectKeyAsync(string projectKey);
        Task<IEnumerable<Issue>> GetActiveByProjectKeyAsync(string projectKey);

        // Get by user assignments
        Task<IEnumerable<Issue>> GetAllByAssigneeAsync(int assigneeId);
        Task<IEnumerable<Issue>> GetAllByReporterAsync(int reporterId);

        // Get by status/type
        Task<IEnumerable<Issue>> GetAllByStatusAsync(int statusId);
        Task<IEnumerable<Issue>> GetAllByIssueTypeAsync(int issueTypeId);

        // Check if projectIssueKey exists
        Task<bool> IsProjectIssueKeyExistsAsync(string projectIssueKey);

        // Generate next issue number for project
        Task<int> GetNextIssueNumberAsync(string projectKey);

        // Soft delete operations
        Task<bool> SoftDeleteAsync(string projectIssueKey);
        Task<bool> RestoreAsync(string projectIssueKey);

        // Assignment operations
        Task<bool> AssignToUserAsync(string projectIssueKey, int assigneeId);
        Task<bool> UnassignAsync(string projectIssueKey);

        // Status operations
        Task<bool> ChangeStatusAsync(string projectIssueKey, int statusId);

        // Get with includes for detailed view
        Task<Issue?> GetByProjectIssueKeyWithDetailsAsync(string projectIssueKey);
        Task<IEnumerable<Issue>> GetAllWithDetailsAsync();
        Task<IEnumerable<Issue>> GetAllByProjectKeyWithDetailsAsync(string projectKey);

        Task<(IEnumerable<Issue> Items, int TotalCount)> GetIssuesByParamsAsync(
            string projectKey,
            int? assigneeId = null,
            int? reporterId = null,
            List<int>? statusIds = null,
            List<int>? issueTypeIds = null,
            List<int>? priorityIds = null,
            List<int>? categoryIds = null,
            List<int>? milestoneIds = null,
            List<int>? versionIds = null,
            DateTime? dueDateFrom = null,
            DateTime? dueDateTo = null,
            DateTime? createdDateFrom = null,
            DateTime? createdDateTo = null,
            string? searchText = null,
            bool includeInactive = false,
            bool includeDetails = true,
            string sortBy = "CreatedAt",
            string sortDirection = "desc",
            int page = 1,
            int pageSize = 20
        );


    }
}
