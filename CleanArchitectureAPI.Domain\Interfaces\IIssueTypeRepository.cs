﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CleanArchitectureAPI.Domain.Entities;
using CleanArchitectureAPI.Domain.Interfaces.Repositories;

namespace CleanArchitectureAPI.Domain.Interfaces
{
    public interface IIssueTypeRepository : IRepository<IssueType>
    {
        Task<bool> IsNameDuplicated(string projectKey, string name);
        Task<int> GetMaxOrderInProjectAsync(string projectKey);

        // Methods for business operations (only active items)
        Task<IEnumerable<IssueType>> GetAllActiveByProjectKeyAsync(string projectKey);

        // Methods for admin/history (include inactive items)
        Task<IEnumerable<IssueType>> GetAllByProjectKeyAsync(string projectKey);

        Task ReorderIssueTypesAsync(string projectKey, List<int> issueTypeIdsInOrder);
    }
}
