using CleanArchitectureAPI.Domain.Interfaces.Repositories;
using CleanArchitectureAPI.Entities.Issue;

namespace CleanArchitectureAPI.Domain.Interfaces
{
    public interface IIssueVersionRepository : IRepository<IssueVersion>
    {
        Task<IEnumerable<IssueVersion>> GetByIssueIdAsync(int issueId);
        Task<IEnumerable<IssueVersion>> GetByVersionIdAsync(int versionId);
        Task<bool> DeleteByIssueIdAsync(int issueId);
        Task<bool> DeleteByVersionIdAsync(int versionId);
        Task<bool> ExistsAsync(int issueId, int versionId);
    }
}
