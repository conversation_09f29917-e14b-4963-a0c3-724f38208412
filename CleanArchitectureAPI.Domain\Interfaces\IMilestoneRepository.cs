using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CleanArchitectureAPI.Domain.Entities;
using CleanArchitectureAPI.Domain.Interfaces.Repositories;

namespace CleanArchitectureAPI.Domain.Interfaces
{
	public interface IMilestoneRepository : IRepository<Milestone>
	{
		Task<bool> IsNameDuplicated(string projectKey, string name);
		Task<int> GetMaxOrderInProjectAsync(string projectKey);

		// Methods for business operations (only active items)
		Task<IEnumerable<Milestone>> GetAllActiveByProjectKeyAsync(string projectKey);

		// Methods for admin/history (include inactive items)
		Task<IEnumerable<Milestone>> GetAllByProjectKeyAsync(string projectKey);

		Task ReorderMilestonesAsync(string projectKey, List<int> milestoneIdsInOrder);
	}
}
