﻿
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CleanArchitectureAPI.Domain.Entities;
using CleanArchitectureAPI.Domain.Entities.User;
using CleanArchitectureAPI.Domain.Interfaces.Repositories;

namespace CleanArchitectureAPI.Domain.Interfaces
{
	public interface IProjectRepository : IRepository<Project>
	{
		public Task<bool> IsNameDuplicated(string name);
		public Task<bool> IsProjectKeyDuplicatedAsync(string projectKey);
		public Task<int?> GetByKeyAsync(string projectKey);
		public Task<IEnumerable<Project>> GetAllProjectByUserIdAsync(int userId);
		public Task<bool> IsUserInProjectAsync(int userId, int projectId);
		public Task<UserProject?> GetUserProjectAsync(int userId, int projectId);
	}
}
