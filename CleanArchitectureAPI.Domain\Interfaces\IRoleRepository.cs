using CleanArchitectureAPI.Domain.Entities.User;
using CleanArchitectureAPI.Domain.Interfaces.Repositories;

namespace CleanArchitectureAPI.Domain.Interfaces
{
    public interface IRoleRepository : IRepository<Role>
    {
        Task<bool> IsNameDuplicated(string projectKey, string name);

        // Methods for business operations (only active items)
        Task<IEnumerable<Role>> GetAllActiveByProjectKeyAsync(string projectKey);
        Task<IEnumerable<Role>> GetActiveByProjectIdAsync(int projectId);

        // Methods for admin/history (include inactive items)
        Task<IEnumerable<Role>> GetAllByProjectKeyAsync(string projectKey);
        Task<IEnumerable<Role>> GetByProjectIdAsync(int projectId);

        Task<Role?> GetByNameAndProjectKeyAsync(string name, string projectKey);
    }
}
