using CleanArchitectureAPI.Domain.Entities.User;
using CleanArchitectureAPI.Domain.Interfaces.Repositories;

namespace CleanArchitectureAPI.Domain.Interfaces
{
    public interface IUserProjectRepository : IRepository<UserProject>
    {
        Task<IEnumerable<UserProject>> GetByUserIdAsync(int userId);
        Task<IEnumerable<UserProject>> GetByProjectIdAsync(int projectId);
        Task<UserProject?> GetByUserAndProjectAsync(int userId, int projectId);
        Task<bool> IsUserInProjectAsync(int userId, int projectId);
        Task<bool> AddUserToProjectAsync(int userId, int projectId, int? roleId = null);
        Task<bool> RemoveUserFromProjectAsync(int userId, int projectId);
        Task<bool> UpdateUserRoleInProjectAsync(int userId, int projectId, int? roleId);
    }
}