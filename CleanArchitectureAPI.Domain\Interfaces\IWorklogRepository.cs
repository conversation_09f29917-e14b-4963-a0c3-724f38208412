using CleanArchitectureAPI.Entities.Issue;

namespace CleanArchitectureAPI.Domain.Interfaces
{
    public interface IWorklogRepository
    {
        Task<Worklog> CreateAsync(Worklog worklog);
        Task<Worklog?> GetByIdAsync(int id);
        Task<IEnumerable<Worklog>> GetByIssueIdAsync(int issueId);
        Task<IEnumerable<Worklog>> GetByAuthorIdAsync(int authorId);
        Task<Worklog> UpdateAsync(Worklog worklog);
        Task<bool> DeleteAsync(int id);
        Task<bool> ExistsAsync(int id);
        Task<IEnumerable<Worklog>> GetPagedAsync(int issueId, int pageNumber, int pageSize);
        Task<int> GetCountByIssueIdAsync(int issueId);
        Task<IEnumerable<Worklog>> GetByDateRangeAsync(int issueId, DateTime fromDate, DateTime toDate);
        Task<int> GetTotalTimeWorkedAsync(int issueId);
        Task<int> GetTotalTimeWorkedByAuthorAsync(int issueId, int authorId);
        Task<IEnumerable<Worklog>> GetByAuthorAndDateRangeAsync(int authorId, DateTime fromDate, DateTime toDate);
        Task<Dictionary<int, int>> GetTimeWorkedByIssueAsync(IEnumerable<int> issueIds);
    }
}
