
using System.Reflection;
using CleanArchitectureAPI.Domain.Entities.User;
using CleanArchitectureAPI.Domain.Entities;
using CleanArchitectureAPI.Infrastructure.Data.Configurations;
using Microsoft.EntityFrameworkCore;
using CleanArchitectureAPI.Entities.Issue;
using CleanArchitectureAPI.Domain.Common;
using DomainVersion = CleanArchitectureAPI.Domain.Entities.Version;

namespace CleanArchitectureAPI.Infrastructure.Data
{
	public class ApplicationDbContext : DbContext
	{
		public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
		{

		}



		public DbSet<AppUser> Users { get; set; }
		public DbSet<Role> Roles { get; set; }
		public DbSet<UserRole> UserRoles { get; set; }
		public DbSet<Permission> Permissions { get; set; }
		public DbSet<RolePermission> RolePermissions { get; set; }
		public DbSet<Group> Groups { get; set; }
		public DbSet<UserGroup> UserGroups { get; set; }
		public DbSet<GroupPermission> GroupPermissions { get; set; }
		public DbSet<UserPermission> UserPermissions { get; set; }
		public DbSet<UserProject> UserProjects { get; set; }

        public DbSet<Project> Projects { get; set; }
        public DbSet<Category> Categories { get; set; }
        public DbSet<Status> Statuses { get; set; }
        public DbSet<Priority> Priorities { get; set; }
        public DbSet<Resolution> Resolutions { get; set; }
        public DbSet<IssueType> IssueTypes { get; set; }
        public DbSet<Milestone> Milestones { get; set; }
        public DbSet<DomainVersion> Versions { get; set; }

        // Issue related entities
        public DbSet<Issue> Issues { get; set; }
        public DbSet<Comment> Comments { get; set; }
        public DbSet<Worklog> Worklogs { get; set; }
        public DbSet<ChangeGroup> ChangeGroups { get; set; }
        public DbSet<ChangeItem> ChangeItems { get; set; }
        public DbSet<FileAttachment> FileAttachments { get; set; }
        public DbSet<IssueCategory> IssueCategories { get; set; }
        public DbSet<IssueMilestone> IssueMilestones { get; set; }
        public DbSet<IssueVersion> IssueVersions { get; set; }
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());
            base.OnModelCreating(modelBuilder);

            // Comment relationships
			modelBuilder.Entity<Comment>()
				.HasOne(c => c.Author)
				.WithMany()
				.HasForeignKey(c => c.AuthorId)
				.OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Comment>()
                .HasOne(c => c.Issue)
                .WithMany()
                .HasForeignKey(c => c.IssueId)
                .OnDelete(DeleteBehavior.Cascade); 

        }
    }
}
