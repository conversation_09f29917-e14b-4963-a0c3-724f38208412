﻿

using CleanArchitectureAPI.Domain.Entities.User;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace CleanArchitectureAPI.Infrastructure.Data.Configurations
{
    public class AppUserConfiguration : BaseEntityConfiguration<AppUser>
    {
        public override void Configure(EntityTypeBuilder<AppUser> builder)
        {
            base.Configure(builder);
            //builder.Property(u => u.FullName)
            //    .HasColumnType("nvarchar(255)")
            //    .IsRequired();

            //builder.Property(u => u.Email)
            //    .HasColumnType("varchar(255)")
            //    .IsRequired();

            //builder.Property(u => u.PasswordHash)
            //    .HasColumnType("varchar(255)")
            //    .IsRequired();

            //builder.Property(u => u.Status)
            //    .HasConversion<string>()
            //    .HasColumnType("nvarchar(60)");

            //builder.Property(u => u.Avatar)
            //    .HasColumnType("nvarchar(500)");

            //builder.Property(u => u.Phone)
            //    .HasColumnType("nvarchar(20)");

            //builder.Property(u => u.Bio)
            //    .HasColumnType("nvarchar(500)");

            //builder.Property(u => u.Address)
            //    .HasColumnType("nvarchar(500)");

            builder.Property(u => u.LastActive)
                .IsRequired();

            builder.Property(u => u.LastLoginTime)
                .IsRequired();

        }

    }
}
