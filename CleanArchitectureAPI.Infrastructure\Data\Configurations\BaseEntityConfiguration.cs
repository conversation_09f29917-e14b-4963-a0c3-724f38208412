﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using CleanArchitectureAPI.Domain.Common;

namespace CleanArchitectureAPI.Infrastructure.Data.Configurations
{
    public abstract class BaseEntityConfiguration<T> : IEntityTypeConfiguration<T> where T : BaseAudit
    {
        public virtual void Configure(EntityTypeBuilder<T> builder)
        {
            builder.Property(e => e.CreatedAt)
                .HasColumnType("datetime2")
                .IsRequired();

            builder.Property(e => e.LastModifiedAt)
                .HasColumnType("datetime2");

            builder.Property(e => e.DeletedAt)
                .HasColumnType("datetime2");
        }
    }
}
