﻿
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using CleanArchitectureAPI.Domain.Entities.User;

namespace CleanArchitectureAPI.Infrastructure.Data.Configurations
{
    public class PermissionConfiguration : BaseEntityConfiguration<Permission>
    {
        public override void Configure(EntityTypeBuilder<Permission> builder)
        {
            base.Configure(builder);

            builder.Property(p => p.Name)
                .HasColumnType("varchar(100)")
                .IsRequired();

            builder.Property(p => p.Description)
                .HasColumnType("nvarchar(256)");

            builder.HasMany(p => p.RolePermissions)
                .WithOne(p => p.Permission)
                .HasForeignKey(p => p.PermissionId)
                .OnDelete(DeleteBehavior.Restrict);
        }
    }
}
