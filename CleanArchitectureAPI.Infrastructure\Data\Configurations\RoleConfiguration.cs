﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using CleanArchitectureAPI.Domain.Entities.User;

namespace CleanArchitectureAPI.Infrastructure.Data.Configurations
{
    public class RoleConfiguration : BaseEntityConfiguration<Role>
    {
        public override void Configure(EntityTypeBuilder<Role> builder)
        {
            base.Configure(builder); 

            builder.Property(r => r.Name)
                .HasMaxLength(100)
                .IsRequired();

            builder.Property(r => r.Description)
                .HasMaxLength(500);

            builder.Property(r => r.ProjectKey)
                .HasMaxLength(50)
                .IsRequired();

            builder.HasIndex(r => new { r.<PERSON>, r.Name })
                .IsUnique();
        }
    }
}
