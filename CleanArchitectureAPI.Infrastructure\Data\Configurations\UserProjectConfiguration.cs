using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using CleanArchitectureAPI.Domain.Entities.User;

namespace CleanArchitectureAPI.Infrastructure.Data.Configurations
{
    public class UserProjectConfiguration : BaseEntityConfiguration<UserProject>
    {
        public override void Configure(EntityTypeBuilder<UserProject> builder)
        {
            base.Configure(builder);

            // Composite key
            builder.HasIndex(up => new { up.UserId, up.ProjectId })
                .IsUnique();

            // Foreign key relationships
            builder.HasOne(up => up.User)
                .WithMany(u => u.UserProjects)
                .HasForeignKey(up => up.UserId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(up => up.Project)
                .WithMany(p => p.UserProjects)
                .HasForeignKey(up => up.ProjectId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(up => up.Role)
                .WithMany()
                .HasForeignKey(up => up.RoleId)
                .OnDelete(DeleteBehavior.NoAction);

            // Properties
            builder.Property(up => up.JoinedAt)
                .IsRequired();

            builder.Property(up => up.IsActive)
                .IsRequired()
                .HasDefaultValue(true);
        }
    }
}
