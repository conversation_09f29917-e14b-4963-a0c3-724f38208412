﻿
using CleanArchitectureAPI.Domain.Interfaces;
using CleanArchitectureAPI.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using CleanArchitectureAPI.Domain.Entities.User;

namespace CleanArchitectureAPI.Infrastructure.Repositories
{
	public class AuthRepository : Repository<AppUser>, IAuthRepository
	{
		public AuthRepository(ApplicationDbContext context) : base(context)
		{
		}

		public AppUser Authenticate(string userName, string password)
		{
			throw new NotImplementedException();
		}

		public async Task<bool> IsEmailExistsAsync(string email)
		{
			return await _context.Users.AnyAsync(u => u.Email == email.ToLower());
		}

		public async Task<AppUser> GetUserByEmailAsync(string email)
		{
			AppUser? user = await _context.Users.FirstOrDefaultAsync(user => user.Email == email);
			 
			

			return user;
		}
	}
}
