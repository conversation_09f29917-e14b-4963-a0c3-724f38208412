using CleanArchitectureAPI.Domain.Interfaces;
using CleanArchitectureAPI.Entities.Issue;
using CleanArchitectureAPI.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;

namespace CleanArchitectureAPI.Infrastructure.Repositories
{
    public class ChangeGroupRepository : IChangeGroupRepository
    {
        private readonly ApplicationDbContext _context;

        public ChangeGroupRepository(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<ChangeGroup> CreateAsync(ChangeGroup changeGroup)
        {
            _context.ChangeGroups.Add(changeGroup);
            await _context.SaveChangesAsync();
            return changeGroup;
        }

        public async Task<ChangeGroup?> GetByIdAsync(int id)
        {
            return await _context.ChangeGroups
                .Include(cg => cg.Author)
                .Include(cg => cg.Issue)
                .FirstOrDefaultAsync(cg => cg.Id == id);
        }

        public async Task<ChangeGroup?> GetByIdWithItemsAsync(int id)
        {
            return await _context.ChangeGroups
                .Include(cg => cg.Author)
                .Include(cg => cg.Issue)
                .Include(cg => cg.ChangeItems.OrderBy(ci => ci.DisplayOrder))
                .Include(cg => cg.Comments.OrderBy(c => c.CreatedAt))
                    .ThenInclude(c => c.Author)
                .FirstOrDefaultAsync(cg => cg.Id == id);
        }

        public async Task<IEnumerable<ChangeGroup>> GetByIssueIdAsync(int issueId)
        {
            return await _context.ChangeGroups
                .Include(cg => cg.Author)
                .Where(cg => cg.IssueId == issueId)
                .OrderByDescending(cg => cg.CreatedAt)
                .ToListAsync();
        }

        public async Task<IEnumerable<ChangeGroup>> GetByIssueIdWithItemsAsync(int issueId)
        {
            return await _context.ChangeGroups
                .Include(cg => cg.Author)
                .Include(cg => cg.ChangeItems.OrderBy(ci => ci.DisplayOrder))
                .Include(cg => cg.Comments.OrderBy(c => c.CreatedAt))
                    .ThenInclude(c => c.Author)
                .Where(cg => cg.IssueId == issueId)
                .OrderByDescending(cg => cg.CreatedAt)
                .ToListAsync();
        }

        public async Task<ChangeGroup> UpdateAsync(ChangeGroup changeGroup)
        {
            _context.ChangeGroups.Update(changeGroup);
            await _context.SaveChangesAsync();
            return changeGroup;
        }

        public async Task<bool> DeleteAsync(int id)
        {
            var changeGroup = await _context.ChangeGroups.FindAsync(id);
            if (changeGroup == null) return false;

            _context.ChangeGroups.Remove(changeGroup);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> ExistsAsync(int id)
        {
            return await _context.ChangeGroups.AnyAsync(cg => cg.Id == id);
        }

        public async Task<IEnumerable<ChangeGroup>> GetPagedAsync(int issueId, int pageNumber, int pageSize)
        {
            return await _context.ChangeGroups
                .Include(cg => cg.Author)
                .Include(cg => cg.ChangeItems.OrderBy(ci => ci.DisplayOrder))
                .Where(cg => cg.IssueId == issueId)
                .OrderByDescending(cg => cg.CreatedAt)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();
        }

        public async Task<int> GetCountByIssueIdAsync(int issueId)
        {
            return await _context.ChangeGroups.CountAsync(cg => cg.IssueId == issueId);
        }

        public async Task<IEnumerable<ChangeGroup>> GetByAuthorIdAsync(int authorId)
        {
            return await _context.ChangeGroups
                .Include(cg => cg.Issue)
                .Include(cg => cg.ChangeItems.OrderBy(ci => ci.DisplayOrder))
                .Where(cg => cg.AuthorId == authorId)
                .OrderByDescending(cg => cg.CreatedAt)
                .ToListAsync();
        }

        public async Task<IEnumerable<ChangeGroup>> GetByDateRangeAsync(int issueId, DateTime fromDate, DateTime toDate)
        {
            return await _context.ChangeGroups
                .Include(cg => cg.Author)
                .Include(cg => cg.ChangeItems.OrderBy(ci => ci.DisplayOrder))
                .Where(cg => cg.IssueId == issueId && 
                            cg.CreatedAt >= fromDate && 
                            cg.CreatedAt <= toDate)
                .OrderByDescending(cg => cg.CreatedAt)
                .ToListAsync();
        }
    }
}
