using CleanArchitectureAPI.Domain.Interfaces;
using CleanArchitectureAPI.Entities.Issue;
using CleanArchitectureAPI.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;

namespace CleanArchitectureAPI.Infrastructure.Repositories
{
    public class ChangeItemRepository : IChangeItemRepository
    {
        private readonly ApplicationDbContext _context;

        public ChangeItemRepository(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<ChangeItem> CreateAsync(ChangeItem changeItem)
        {
            _context.ChangeItems.Add(changeItem);
            await _context.SaveChangesAsync();
            return changeItem;
        }

        public async Task<ChangeItem?> GetByIdAsync(int id)
        {
            return await _context.ChangeItems
                .Include(ci => ci.ChangeGroup)
                    .ThenInclude(cg => cg.Issue)
                .FirstOrDefaultAsync(ci => ci.Id == id);
        }

        public async Task<IEnumerable<ChangeItem>> GetByGroupIdAsync(int groupId)
        {
            return await _context.ChangeItems
                .Where(ci => ci.GroupId == groupId)
                .OrderBy(ci => ci.DisplayOrder)
                .ToListAsync();
        }

        public async Task<ChangeItem> UpdateAsync(ChangeItem changeItem)
        {
            _context.ChangeItems.Update(changeItem);
            await _context.SaveChangesAsync();
            return changeItem;
        }

        public async Task<bool> DeleteAsync(int id)
        {
            var changeItem = await _context.ChangeItems.FindAsync(id);
            if (changeItem == null) return false;

            _context.ChangeItems.Remove(changeItem);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> ExistsAsync(int id)
        {
            return await _context.ChangeItems.AnyAsync(ci => ci.Id == id);
        }

        public async Task<IEnumerable<ChangeItem>> GetByFieldAsync(int issueId, string field)
        {
            return await _context.ChangeItems
                .Include(ci => ci.ChangeGroup)
                .Where(ci => ci.ChangeGroup.IssueId == issueId && ci.Field == field)
                .OrderByDescending(ci => ci.CreatedAt)
                .ToListAsync();
        }

        public async Task<IEnumerable<ChangeItem>> GetByFieldTypeAsync(int issueId, string fieldType)
        {
            return await _context.ChangeItems
                .Include(ci => ci.ChangeGroup)
                .Where(ci => ci.ChangeGroup.IssueId == issueId && ci.FieldType == fieldType)
                .OrderByDescending(ci => ci.CreatedAt)
                .ToListAsync();
        }

        public async Task<bool> DeleteByGroupIdAsync(int groupId)
        {
            var changeItems = await _context.ChangeItems
                .Where(ci => ci.GroupId == groupId)
                .ToListAsync();

            if (!changeItems.Any()) return false;

            _context.ChangeItems.RemoveRange(changeItems);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<IEnumerable<ChangeItem>> CreateBulkAsync(IEnumerable<ChangeItem> changeItems)
        {
            var itemsList = changeItems.ToList();
            _context.ChangeItems.AddRange(itemsList);
            await _context.SaveChangesAsync();
            return itemsList;
        }
    }
}
