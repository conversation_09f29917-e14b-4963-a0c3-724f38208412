using CleanArchitectureAPI.Domain.Interfaces;
using CleanArchitectureAPI.Entities.Issue;
using CleanArchitectureAPI.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;

namespace CleanArchitectureAPI.Infrastructure.Repositories
{
    public class CommentRepository : ICommentRepository
    {
        private readonly ApplicationDbContext _context;

        public CommentRepository(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<Comment> CreateAsync(Comment comment)
        {
            _context.Comments.Add(comment);
            await _context.SaveChangesAsync();
            return comment;
        }

        public async Task<Comment?> GetByIdAsync(int id)
        {
            return await _context.Comments
                .Include(c => c.Author)
                .Include(c => c.Issue)
                .Include(c => c.ChangeGroup)
                .FirstOrDefaultAsync(c => c.Id == id);
        }

        public async Task<IEnumerable<Comment>> GetByIssueIdAsync(int issueId)
        {
            return await _context.Comments
                .Include(c => c.Author)
                .Include(c => c.ChangeGroup)
                .Where(c => c.IssueId == issueId)
                .OrderBy(c => c.CreatedAt)
                .ToListAsync();
        }

        public async Task<IEnumerable<Comment>> GetByChangeGroupIdAsync(int changeGroupId)
        {
            return await _context.Comments
                .Include(c => c.Author)
                .Where(c => c.ChangeGroupId == changeGroupId)
                .OrderBy(c => c.CreatedAt)
                .ToListAsync();
        }

        public async Task<Comment> UpdateAsync(Comment comment)
        {
            comment.IsEdited = true;
            comment.LastEditedAt = DateTime.UtcNow;
            _context.Comments.Update(comment);
            await _context.SaveChangesAsync();
            return comment;
        }

        public async Task<bool> DeleteAsync(int id)
        {
            var comment = await _context.Comments.FindAsync(id);
            if (comment == null) return false;

            _context.Comments.Remove(comment);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> ExistsAsync(int id)
        {
            return await _context.Comments.AnyAsync(c => c.Id == id);
        }

        public async Task<IEnumerable<Comment>> GetPagedAsync(int issueId, int pageNumber, int pageSize)
        {
            return await _context.Comments
                .Include(c => c.Author)
                .Include(c => c.ChangeGroup)
                .Where(c => c.IssueId == issueId)
                .OrderBy(c => c.CreatedAt)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();
        }

        public async Task<int> GetCountByIssueIdAsync(int issueId)
        {
            return await _context.Comments.CountAsync(c => c.IssueId == issueId);
        }
    }
}
