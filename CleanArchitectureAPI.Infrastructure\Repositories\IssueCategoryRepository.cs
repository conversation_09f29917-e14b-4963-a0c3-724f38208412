using CleanArchitectureAPI.Domain.Interfaces;
using CleanArchitectureAPI.Entities.Issue;
using CleanArchitectureAPI.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;

namespace CleanArchitectureAPI.Infrastructure.Repositories
{
    public class IssueCategoryRepository : Repository<IssueCategory>, IIssueCategoryRepository
    {
        public IssueCategoryRepository(ApplicationDbContext context) : base(context)
        {
        }

        public async Task<IEnumerable<IssueCategory>> GetByIssueIdAsync(int issueId)
        {
            return await _context.IssueCategories
                .Include(ic => ic.Category)
                .Where(ic => ic.IssueId == issueId && !ic.IsDeleted)
                .ToListAsync();
        }

        public async Task<IEnumerable<IssueCategory>> GetByCategoryIdAsync(int categoryId)
        {
            return await _context.IssueCategories
                .Include(ic => ic.Issue)
                .Where(ic => ic.CategoryId == categoryId && !ic.IsDeleted)
                .ToListAsync();
        }

        public async Task<bool> DeleteByIssueIdAsync(int issueId)
        {
            var issueCategories = await _context.IssueCategories
                .Where(ic => ic.IssueId == issueId && !ic.IsDeleted)
                .ToListAsync();

            foreach (var issueCategory in issueCategories)
            {
                issueCategory.IsDeleted = true;
                issueCategory.LastModifiedAt = DateTime.UtcNow;
            }

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> DeleteByCategoryIdAsync(int categoryId)
        {
            var issueCategories = await _context.IssueCategories
                .Where(ic => ic.CategoryId == categoryId && !ic.IsDeleted)
                .ToListAsync();

            foreach (var issueCategory in issueCategories)
            {
                issueCategory.IsDeleted = true;
                issueCategory.LastModifiedAt = DateTime.UtcNow;
            }

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> ExistsAsync(int issueId, int categoryId)
        {
            return await _context.IssueCategories
                .AnyAsync(ic => ic.IssueId == issueId && ic.CategoryId == categoryId && !ic.IsDeleted);
        }
    }
}
