using CleanArchitectureAPI.Domain.Interfaces;
using CleanArchitectureAPI.Entities.Issue;
using CleanArchitectureAPI.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;

namespace CleanArchitectureAPI.Infrastructure.Repositories
{
    public class IssueMilestoneRepository : Repository<IssueMilestone>, IIssueMilestoneRepository
    {
        public IssueMilestoneRepository(ApplicationDbContext context) : base(context)
        {
        }

        public async Task<IEnumerable<IssueMilestone>> GetByIssueIdAsync(int issueId)
        {
            return await _context.IssueMilestones
                .Include(im => im.Milestone)
                .Where(im => im.IssueId == issueId && !im.IsDeleted)
                .ToListAsync();
        }

        public async Task<IEnumerable<IssueMilestone>> GetByMilestoneIdAsync(int milestoneId)
        {
            return await _context.IssueMilestones
                .Include(im => im.Issue)
                .Where(im => im.MilestoneId == milestoneId && !im.IsDeleted)
                .ToListAsync();
        }

        public async Task<bool> DeleteByIssueIdAsync(int issueId)
        {
            var issueMilestones = await _context.IssueMilestones
                .Where(im => im.IssueId == issueId && !im.IsDeleted)
                .ToListAsync();

            foreach (var issueMilestone in issueMilestones)
            {
                issueMilestone.IsDeleted = true;
                issueMilestone.LastModifiedAt = DateTime.UtcNow;
            }

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> DeleteByMilestoneIdAsync(int milestoneId)
        {
            var issueMilestones = await _context.IssueMilestones
                .Where(im => im.MilestoneId == milestoneId && !im.IsDeleted)
                .ToListAsync();

            foreach (var issueMilestone in issueMilestones)
            {
                issueMilestone.IsDeleted = true;
                issueMilestone.LastModifiedAt = DateTime.UtcNow;
            }

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> ExistsAsync(int issueId, int milestoneId)
        {
            return await _context.IssueMilestones
                .AnyAsync(im => im.IssueId == issueId && im.MilestoneId == milestoneId && !im.IsDeleted);
        }
    }
}
