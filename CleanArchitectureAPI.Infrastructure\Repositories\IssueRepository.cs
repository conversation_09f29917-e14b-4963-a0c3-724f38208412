using CleanArchitectureAPI.Application.DTOs;
using CleanArchitectureAPI.Domain.Interfaces;
using CleanArchitectureAPI.Entities.Issue;
using CleanArchitectureAPI.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;

namespace CleanArchitectureAPI.Infrastructure.Repositories
{
    public class IssueRepository : Repository<Issue>, IIssueRepository
    {
        public IssueRepository(ApplicationDbContext context) : base(context)
        {
        }

        public async Task<Issue?> GetByProjectIssueKeyAsync(string projectIssueKey)
        {
            return await _context.Issues
                .FirstOrDefaultAsync(i => i.ProjectIssueKey == projectIssueKey && !i.IsDeleted);
        }

        public async Task<IEnumerable<Issue>> GetAllByProjectKeyAsync(string projectKey)
        {
            return await _context.Issues
                .Where(i => i.ProjectKey == projectKey && !i.IsDeleted)
                .OrderByDescending(i => i.CreatedAt)
                .ToListAsync();
        }

        public async Task<IEnumerable<Issue>> GetActiveByProjectKeyAsync(string projectKey)
        {
            return await _context.Issues
                .Where(i => i.ProjectKey == projectKey && !i.IsDeleted && i.IsActive)
                .OrderByDescending(i => i.CreatedAt)
                .ToListAsync();
        }

        public async Task<IEnumerable<Issue>> GetAllByAssigneeAsync(int assigneeId)
        {
            return await _context.Issues
                .Where(i => i.AssigneeId == assigneeId && !i.IsDeleted && i.IsActive)
                .OrderByDescending(i => i.CreatedAt)
                .ToListAsync();
        }

        public async Task<IEnumerable<Issue>> GetAllByReporterAsync(int reporterId)
        {
            return await _context.Issues
                .Where(i => i.ReporterId == reporterId && !i.IsDeleted && i.IsActive)
                .OrderByDescending(i => i.CreatedAt)
                .ToListAsync();
        }

        public async Task<IEnumerable<Issue>> GetAllByStatusAsync(int statusId)
        {
            return await _context.Issues
                .Where(i => i.StatusId == statusId && !i.IsDeleted && i.IsActive)
                .OrderByDescending(i => i.CreatedAt)
                .ToListAsync();
        }

        public async Task<IEnumerable<Issue>> GetAllByIssueTypeAsync(int issueTypeId)
        {
            return await _context.Issues
                .Where(i => i.IssueTypeId == issueTypeId && !i.IsDeleted && i.IsActive)
                .OrderByDescending(i => i.CreatedAt)
                .ToListAsync();
        }

        public async Task<bool> IsProjectIssueKeyExistsAsync(string projectIssueKey)
        {
            return await _context.Issues
                .AnyAsync(i => i.ProjectIssueKey == projectIssueKey && !i.IsDeleted);
        }

        public async Task<int> GetNextIssueNumberAsync(string projectKey)
        {
            var project = await _context.Projects
                .FirstOrDefaultAsync(p => p.ProjectKey == projectKey && !p.IsDeleted);
            
            if (project == null)
                return 1;

            // Increment and update the Last_Issue_Number
            project.Last_Issue_Number++;
            await _context.SaveChangesAsync();
            
            return project.Last_Issue_Number;
        }

        public async Task<bool> SoftDeleteAsync(string projectIssueKey)
        {
            var issue = await GetByProjectIssueKeyAsync(projectIssueKey);
            if (issue == null)
                return false;

            issue.IsDeleted = true;
            issue.LastModifiedAt = DateTime.UtcNow;
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> RestoreAsync(string projectIssueKey)
        {
            var issue = await _context.Issues
                .FirstOrDefaultAsync(i => i.ProjectIssueKey == projectIssueKey && i.IsDeleted);
            
            if (issue == null)
                return false;

            issue.IsDeleted = false;
            issue.LastModifiedAt = DateTime.UtcNow;
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> AssignToUserAsync(string projectIssueKey, int assigneeId)
        {
            var issue = await GetByProjectIssueKeyAsync(projectIssueKey);
            if (issue == null)
                return false;

            issue.AssigneeId = assigneeId;
            issue.LastModifiedAt = DateTime.UtcNow;
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> UnassignAsync(string projectIssueKey)
        {
            var issue = await GetByProjectIssueKeyAsync(projectIssueKey);
            if (issue == null)
                return false;

            issue.AssigneeId = null;
            issue.LastModifiedAt = DateTime.UtcNow;
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> ChangeStatusAsync(string projectIssueKey, int statusId)
        {
            var issue = await GetByProjectIssueKeyAsync(projectIssueKey);
            if (issue == null)
                return false;

            issue.StatusId = statusId;
            issue.LastModifiedAt = DateTime.UtcNow;
            await _context.SaveChangesAsync();
            return true;
        }

        //public async Task<bool> ChangeResolutionAsync(string projectIssueKey, int resolutionId)
        //{
        //    var issue = await GetByProjectIssueKeyAsync(projectIssueKey);
        //    if (issue == null)
        //        return false;

        //    issue.ResolutionId = resolutionId;
        //    issue.LastModifiedAt = DateTime.UtcNow;
        //    await _context.SaveChangesAsync();
        //    return true;
        //}

        public async Task<Issue?> GetByProjectIssueKeyWithDetailsAsync(string projectIssueKey)
        {
            return await _context.Issues
                .Include(i => i.Assignee)
                .Include(i => i.Reporter)
                .Include(i => i.IssueType)
                .Include(i => i.Status)
                .Include(i => i.Priority)
                .Include(i => i.IssueCategories).ThenInclude(ic => ic.Category)
                .Include(i => i.IssueMilestones).ThenInclude(im => im.Milestone)
                .Include(i => i.IssueVersions).ThenInclude(iv => iv.Version)
                .Include(i => i.Comments)
                .FirstOrDefaultAsync(i => i.ProjectIssueKey == projectIssueKey && !i.IsDeleted);
        }

        public async Task<IEnumerable<Issue>> GetAllWithDetailsAsync()
        {
            return await _context.Issues
                .Include(i => i.Assignee)
                .Include(i => i.Reporter)
                .Include(i => i.IssueType)
                .Include(i => i.Status)
                .Include(i => i.Priority)
                .Include(i => i.IssueCategories).ThenInclude(ic => ic.Category)
                .Include(i => i.IssueMilestones).ThenInclude(im => im.Milestone)
                .Include(i => i.IssueVersions).ThenInclude(iv => iv.Version)
                .Where(i => !i.IsDeleted && i.IsActive)
                .OrderByDescending(i => i.CreatedAt)
                .ToListAsync();
        }

        public async Task<IEnumerable<Issue>> GetAllByProjectKeyWithDetailsAsync(string projectKey)
        {
            return await _context.Issues
                .Include(i => i.Assignee)
                .Include(i => i.Reporter)
                .Include(i => i.IssueType)
                .Include(i => i.Status)
                .Include(i => i.Priority)
                .Include(i => i.IssueCategories).ThenInclude(ic => ic.Category)
                .Include(i => i.IssueMilestones).ThenInclude(im => im.Milestone)
                .Include(i => i.IssueVersions).ThenInclude(iv => iv.Version)
                .Where(i => i.ProjectKey == projectKey && !i.IsDeleted && i.IsActive)
                .OrderByDescending(i => i.CreatedAt)
                .ToListAsync();
        }

        public async Task<(IEnumerable<Issue> Items, int TotalCount)> GetIssuesByParamsAsync(
            string projectKey,
            int? assigneeId = null,
            int? reporterId = null,
            List<int>? statusIds = null,
            List<int>? issueTypeIds = null,
            List<int>? priorityIds = null,
            List<int>? categoryIds = null,
            List<int>? milestoneIds = null,
            List<int>? versionIds = null,
            DateTime? dueDateFrom = null,
            DateTime? dueDateTo = null,
            DateTime? createdDateFrom = null,
            DateTime? createdDateTo = null,
            string? searchText = null,
            bool includeInactive = false,
            bool includeDetails = true,
            string sortBy = "CreatedAt",
            string sortDirection = "desc",
            int page = 1,
            int pageSize = 20)
        {
            // Bắt đầu truy vấn từ bảng Issues
            IQueryable<Issue> query = _context.Issues;

            
                query = query
                    .Include(i => i.Assignee)
                    .Include(i => i.Reporter)
                    .Include(i => i.IssueType)
                    .Include(i => i.Status)
                    .Include(i => i.Priority)
                    .Include(i => i.IssueCategories).ThenInclude(ic => ic.Category)
                    .Include(i => i.IssueMilestones).ThenInclude(im => im.Milestone)
                    .Include(i => i.IssueVersions).ThenInclude(iv => iv.Version)
                    .Include(i => i.Comments);

            // Lọc theo projectKey và loại bỏ các issue đã bị xóa
            query = query.Where(i => i.ProjectKey == projectKey && !i.IsDeleted);

            // Nếu không lấy các issue không active thì lọc tiếp
            if (!includeInactive)
            {
                query = query.Where(i => i.IsActive);
            }

            // Lọc theo assigneeId nếu có
            if (assigneeId != null)
            {
                query = query.Where(i => i.AssigneeId == assigneeId.Value);
            }
        
            // Lọc theo reporterId nếu có
            if (reporterId != null)
            {
                query = query.Where(i => i.ReporterId == reporterId.Value);
            }

            // Lọc theo danh sách statusIds nếu có
            if (statusIds != null && statusIds.Count > 0)
            {
                query = query.Where(i => statusIds.Contains(i.StatusId));
            }

            // Lọc theo danh sách issueTypeIds nếu có
            if (issueTypeIds != null && issueTypeIds.Count > 0)
            {
                query = query.Where(i => issueTypeIds.Contains(i.IssueTypeId));
            }

            // Lọc theo danh sách priorityIds nếu có
            if (priorityIds != null && priorityIds.Count > 0)
            {
                query = query.Where(i => priorityIds.Contains(i.PriorityId));
            }

            // Lọc theo danh sách categoryIds nếu có
            if (categoryIds != null && categoryIds.Count > 0)
            {
                query = query.Where(i => i.IssueCategories.Any(ic => categoryIds.Contains(ic.CategoryId) && !ic.IsDeleted));
            }

            // Lọc theo danh sách milestoneIds nếu có
            if (milestoneIds != null && milestoneIds.Count > 0)
            {
                query = query.Where(i => i.IssueMilestones.Any(im => milestoneIds.Contains(im.MilestoneId) && !im.IsDeleted));
            }

            // Lọc theo danh sách versionIds nếu có
            if (versionIds != null && versionIds.Count > 0)
            {
                query = query.Where(i => i.IssueVersions.Any(iv => versionIds.Contains(iv.VersionId) && !iv.IsDeleted));
            }

            // Lọc theo ngày đến hạn từ
            if (dueDateFrom != null)
            {
                query = query.Where(i => i.DueDate >= dueDateFrom.Value);
            }

            // Lọc theo ngày đến hạn đến
            if (dueDateTo != null)
            {
                query = query.Where(i => i.DueDate <= dueDateTo.Value);
            }

            // Lọc theo ngày tạo từ
            if (createdDateFrom != null)
            {
                query = query.Where(i => i.CreatedAt >= createdDateFrom.Value);
            }

            // Lọc theo ngày tạo đến
            if (createdDateTo != null)
            {
                query = query.Where(i => i.CreatedAt <= createdDateTo.Value);
            }

            // Tìm kiếm theo text nếu có
            if (!string.IsNullOrWhiteSpace(searchText))
            {
                string searchLower = searchText.ToLower();
                query = query.Where(i =>
                    i.Subject.ToLower().Contains(searchLower) ||
                    (i.Description != null && i.Description.ToLower().Contains(searchLower)) ||
                    i.ProjectIssueKey.ToLower().Contains(searchLower)
                );
            }

            // Đếm tổng số bản ghi sau khi lọc
            int totalCount = await query.CountAsync();

            // Sắp xếp theo trường được chỉ định
            query = ApplySorting(query, sortBy, sortDirection);

            // Phân trang
            int skip = (page - 1) * pageSize;
            List<Issue> items = await query.Skip(skip).Take(pageSize).ToListAsync();

            // Trả về kết quả
            return (items, totalCount);
        }

        private IQueryable<Issue> ApplySorting(IQueryable<Issue> query, string sortBy, string sortDirection)
        {
            var isDescending = sortDirection.ToLower() == "desc";

            return sortBy.ToLower() switch
            {
                "subject" => isDescending ? query.OrderByDescending(i => i.Subject) : query.OrderBy(i => i.Subject),
                "projectissuekey" => isDescending ? query.OrderByDescending(i => i.ProjectIssueKey) : query.OrderBy(i => i.ProjectIssueKey),
                "assignee" => isDescending ? query.OrderByDescending(i => i.Assignee!.FullName) : query.OrderBy(i => i.Assignee!.FullName),
                "reporter" => isDescending ? query.OrderByDescending(i => i.Reporter!.FullName) : query.OrderBy(i => i.Reporter!.FullName),
                "status" => isDescending ? query.OrderByDescending(i => i.Status!.Name) : query.OrderBy(i => i.Status!.Name),
                "issuetype" => isDescending ? query.OrderByDescending(i => i.IssueType!.Name) : query.OrderBy(i => i.IssueType!.Name),
                "priority" => isDescending ? query.OrderByDescending(i => i.Priority!.Name) : query.OrderBy(i => i.Priority!.Name),
                "duedate" => isDescending ? query.OrderByDescending(i => i.DueDate) : query.OrderBy(i => i.DueDate),
                "lastmodifiedat" => isDescending ? query.OrderByDescending(i => i.LastModifiedAt) : query.OrderBy(i => i.LastModifiedAt),
                _ => isDescending ? query.OrderByDescending(i => i.CreatedAt) : query.OrderBy(i => i.CreatedAt)
            };
        }
    }
}
