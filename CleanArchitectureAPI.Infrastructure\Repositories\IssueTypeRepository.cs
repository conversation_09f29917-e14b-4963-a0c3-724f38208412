﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CleanArchitectureAPI.Domain.Entities;
using CleanArchitectureAPI.Domain.Interfaces;
using CleanArchitectureAPI.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;

namespace CleanArchitectureAPI.Infrastructure.Repositories
{
    public class IssueTypeRepository : Repository<IssueType>, IIssueTypeRepository
    {
        public IssueTypeRepository(ApplicationDbContext context) : base(context)
        {
        }

        // For business operations - only active items
        public async Task<IEnumerable<IssueType>> GetAllActiveByProjectKeyAsync(string projectKey)
        {
            var issueTypes = await _context.IssueTypes.Where(c => c.Project<PERSON>ey == projectKey && !c.IsDeleted && c.IsActive).OrderBy(c => c.Order).ToListAsync();
            return issueTypes;
        }

        // For admin/history - include inactive items
        public async Task<IEnumerable<IssueType>> GetAllByProjectKeyAsync(string projectKey)
        {
            var issueTypes = await _context.IssueTypes.Where(c => c.ProjectKey == projectKey && !c.Is<PERSON>eleted).OrderBy(c => c.Order).ToListAsync();
            return issueTypes;
        }

        public async Task<int> GetMaxOrderInProjectAsync(string projectKey)
        {
            return await _context.IssueTypes
                .Where(c => c.ProjectKey == projectKey && !c.IsDeleted && c.IsActive)
                .Select(c => (int?)c.Order)
                .MaxAsync() ?? 0;
        }

        public async Task<bool> IsNameDuplicated(string projectKey, string name)
        {
            return await _context.IssueTypes.AnyAsync(p => p.Name == name && p.ProjectKey == projectKey && !p.IsDeleted && p.IsActive);
        }

        public async Task ReorderIssueTypesAsync(string projectKey, List<int> issueTypeIdsInOrder)
        {
            var issueTypes = await _context.IssueTypes
                .Where(s => s.ProjectKey == projectKey && !s.IsDeleted && s.IsActive && issueTypeIdsInOrder.Contains(s.Id))
                .ToListAsync();

            for (int i = 0; i < issueTypeIdsInOrder.Count; i++)
            {
                var issueType = issueTypes.FirstOrDefault(s => s.Id == issueTypeIdsInOrder[i]);
                if (issueType != null)
                {
                    issueType.Order = i + 1;
                    _context.IssueTypes.Update(issueType);
                }
            }

            await _context.SaveChangesAsync();
        }
    }
}
