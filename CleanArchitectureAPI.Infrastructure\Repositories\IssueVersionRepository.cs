using CleanArchitectureAPI.Domain.Interfaces;
using CleanArchitectureAPI.Entities.Issue;
using CleanArchitectureAPI.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;

namespace CleanArchitectureAPI.Infrastructure.Repositories
{
    public class IssueVersionRepository : Repository<IssueVersion>, IIssueVersionRepository
    {
        public IssueVersionRepository(ApplicationDbContext context) : base(context)
        {
        }

        public async Task<IEnumerable<IssueVersion>> GetByIssueIdAsync(int issueId)
        {
            return await _context.IssueVersions
                .Include(iv => iv.Version)
                .Where(iv => iv.IssueId == issueId && !iv.IsDeleted)
                .ToListAsync();
        }

        public async Task<IEnumerable<IssueVersion>> GetByVersionIdAsync(int versionId)
        {
            return await _context.IssueVersions
                .Include(iv => iv.Issue)
                .Where(iv => iv.VersionId == versionId && !iv.IsDeleted)
                .ToListAsync();
        }

        public async Task<bool> DeleteByIssueIdAsync(int issueId)
        {
            var issueVersions = await _context.IssueVersions
                .Where(iv => iv.IssueId == issueId && !iv.IsDeleted)
                .ToListAsync();

            foreach (var issueVersion in issueVersions)
            {
                issueVersion.IsDeleted = true;
                issueVersion.LastModifiedAt = DateTime.UtcNow;
            }

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> DeleteByVersionIdAsync(int versionId)
        {
            var issueVersions = await _context.IssueVersions
                .Where(iv => iv.VersionId == versionId && !iv.IsDeleted)
                .ToListAsync();

            foreach (var issueVersion in issueVersions)
            {
                issueVersion.IsDeleted = true;
                issueVersion.LastModifiedAt = DateTime.UtcNow;
            }

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> ExistsAsync(int issueId, int versionId)
        {
            return await _context.IssueVersions
                .AnyAsync(iv => iv.IssueId == issueId && iv.VersionId == versionId && !iv.IsDeleted);
        }
    }
}
