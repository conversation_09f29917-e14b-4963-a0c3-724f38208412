using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CleanArchitectureAPI.Domain.Entities;
using CleanArchitectureAPI.Domain.Interfaces;
using CleanArchitectureAPI.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;

namespace CleanArchitectureAPI.Infrastructure.Repositories
{
	public class MilestoneRepository : Repository<Milestone>, IMilestoneRepository
	{
		public MilestoneRepository(ApplicationDbContext context) : base(context)
		{

		}

		public async Task<bool> IsNameDuplicated(string projectKey, string name)
		{
			return await _context.Milestones.AnyAsync(p => p.Name == name && p.ProjectKey == projectKey && !p.IsDeleted && p.IsActive);
		}

		public async Task<int> GetMaxOrderInProjectAsync(string projectKey)
		{
			return await _context.Milestones
				.Where(c => c.ProjectKey == projectKey && !c.IsDeleted && c.IsActive)
				.Select(c => (int?)c.Order)
				.MaxAsync() ?? 0;
		}

		// For business operations - only active items
		public async Task<IEnumerable<Milestone>> GetAllActiveByProjectKeyAsync(string projectKey)
		{
			var milestones = await _context.Milestones.Where(c => c.ProjectKey == projectKey && !c.IsDeleted && c.IsActive).OrderBy(c => c.Order).ToListAsync();
			return milestones;
		}

		// For admin/history - include inactive items
		public async Task<IEnumerable<Milestone>> GetAllByProjectKeyAsync(string projectKey)
		{
			var milestones = await _context.Milestones.Where(c => c.ProjectKey == projectKey && !c.IsDeleted).OrderBy(c => c.Order).ToListAsync();
			return milestones;
		}

		public async Task ReorderMilestonesAsync(string projectKey, List<int> milestoneIdsInOrder)
		{
			var milestones = await _context.Milestones
				.Where(s => s.ProjectKey == projectKey && !s.IsDeleted && s.IsActive && milestoneIdsInOrder.Contains(s.Id))
				.ToListAsync();

			for (int i = 0; i < milestoneIdsInOrder.Count; i++)
			{
				var milestone = milestones.FirstOrDefault(s => s.Id == milestoneIdsInOrder[i]);
				if (milestone != null)
				{
					milestone.Order = i + 1;
					_context.Milestones.Update(milestone);
				}
			}

			await _context.SaveChangesAsync();
		}
	}
}
