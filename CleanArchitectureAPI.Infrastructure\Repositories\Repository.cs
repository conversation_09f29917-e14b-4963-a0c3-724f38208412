﻿

using System.Linq.Expressions;
using CleanArchitectureAPI.Domain.Common;
using CleanArchitectureAPI.Domain.Interfaces.Repositories;
using CleanArchitectureAPI.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;

namespace CleanArchitectureAPI.Infrastructure.Repositories
{
	public class Repository<T> : IRepository<T> where T : BaseAudit
	{
		protected readonly ApplicationDbContext _context;

		public Repository(ApplicationDbContext context)
		{
			_context = context;
		}
		
		public async Task<T> CreateAsync(T entity)
		{
			_context.Set<T>().Add(entity);
			await _context.SaveChangesAsync();
			return entity;
		}

		public async Task<bool> SoftDeleteAsync(int id)
		{
			T? entity = await _context.Set<T>().FindAsync(id);
			if (entity == null) return false;

			entity.IsDeleted = true;
			_context.Set<T>().Update(entity);
			await _context.SaveChangesAsync();
			return true;
		}

		public async Task<bool> ExistsAsync(int id)
		{
			return await _context.Set<T>().AnyAsync(x => x.Id == id && !x.Is<PERSON>ted);
		}
		public Task<IQueryable<T>> GetAllAsync()
		{
			return (Task<IQueryable<T>>)_context.Set<T>().Where(x => !x.IsDeleted && x.IsActive);
		}

		public async Task<T?> GetByIdAsync(int id)
		{
			return await _context.Set<T>().FirstOrDefaultAsync(x => x.Id == id && !x.IsDeleted && x.IsActive);
		}

		public async Task<IEnumerable<T>> GetByIdsAsync(List<int> ids)
		{
			return await _context.Set<T>().Where(p=>ids.Contains(p.Id)).ToListAsync();
		}

		public async Task<T> UpdateAsync(T entity)
		{
			_context.Set<T>().Update(entity);
			await _context.SaveChangesAsync();
			return entity;
		}
	}
}
