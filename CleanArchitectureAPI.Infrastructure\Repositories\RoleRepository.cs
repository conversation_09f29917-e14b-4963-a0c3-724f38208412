using Microsoft.EntityFrameworkCore;
using CleanArchitectureAPI.Domain.Entities.User;
using CleanArchitectureAPI.Domain.Interfaces;
using CleanArchitectureAPI.Infrastructure.Data;

namespace CleanArchitectureAPI.Infrastructure.Repositories
{
    public class RoleRepository : Repository<Role>, IRoleRepository
    {
        public RoleRepository(ApplicationDbContext context) : base(context)
        {
        }

        public async Task<bool> IsNameDuplicated(string projectKey, string name)
        {
            return await _context.Roles.AnyAsync(r => r.Name == name && r.ProjectKey == projectKey && !r.IsDeleted && r.IsActive);
        }

        // For business operations - only active items
        public async Task<IEnumerable<Role>> GetAllActiveByProjectKeyAsync(string projectKey)
        {
            return await _context.Roles
                .Where(r => r.ProjectKey == projectKey && !r.IsDeleted && r.IsActive)
                .OrderBy(r => r.Name)
                .ToListAsync();
        }

        // For admin/history - include inactive items
        public async Task<IEnumerable<Role>> GetAllByProjectKeyAsync(string projectKey)
        {
            return await _context.Roles
                .Where(r => r.ProjectKey == projectKey && !r.IsDeleted)
                .OrderBy(r => r.Name)
                .ToListAsync();
        }

        public async Task<Role?> GetByNameAndProjectKeyAsync(string name, string projectKey)
        {
            return await _context.Roles
                .FirstOrDefaultAsync(r => r.Name == name && r.ProjectKey == projectKey && !r.IsDeleted && r.IsActive);
        }

        // For business operations - only active items
        public async Task<IEnumerable<Role>> GetActiveByProjectIdAsync(int projectId)
        {
            return await _context.Roles
                .Where(r => r.ProjectId == projectId && !r.IsDeleted && r.IsActive)
                .OrderBy(r => r.Name)
                .ToListAsync();
        }

        // For admin/history - include inactive items
        public async Task<IEnumerable<Role>> GetByProjectIdAsync(int projectId)
        {
            return await _context.Roles
                .Where(r => r.ProjectId == projectId && !r.IsDeleted)
                .OrderBy(r => r.Name)
                .ToListAsync();
        }
    }
}
