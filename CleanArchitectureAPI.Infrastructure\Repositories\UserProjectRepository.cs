using Microsoft.EntityFrameworkCore;
using CleanArchitectureAPI.Domain.Entities.User;
using CleanArchitectureAPI.Domain.Interfaces;
using CleanArchitectureAPI.Infrastructure.Data;

namespace CleanArchitectureAPI.Infrastructure.Repositories
{
    public class UserProjectRepository : Repository<UserProject>, IUserProjectRepository
    {
        public UserProjectRepository(ApplicationDbContext context) : base(context)
        {
        }

        public async Task<IEnumerable<UserProject>> GetByUserIdAsync(int userId)
        {
            return await _context.UserProjects
                .Include(up => up.Project)
                .Include(up => up.Role)
                .Where(up => up.UserId == userId && up.IsActive)
                .ToListAsync();
        }

        public async Task<IEnumerable<UserProject>> GetByProjectIdAsync(int projectId)
        {
            return await _context.UserProjects
                .Include(up => up.User)
                .Include(up => up.Role)
                .Where(up => up.ProjectId == projectId && up.IsActive)
                .ToListAsync();
        }

        public async Task<UserProject?> GetByUserAndProjectAsync(int userId, int projectId)
        {
            return await _context.UserProjects
                .Include(up => up.User)
                .Include(up => up.Project)
                .Include(up => up.Role)
                .FirstOrDefaultAsync(up => up.UserId == userId && up.ProjectId == projectId);
        }

        public async Task<bool> IsUserInProjectAsync(int userId, int projectId)
        {
            return await _context.UserProjects
                .AnyAsync(up => up.UserId == userId && up.ProjectId == projectId && up.IsActive);
        }

        public async Task<bool> AddUserToProjectAsync(int userId, int projectId, int? roleId = null)
        {
            var existingUserProject = await GetByUserAndProjectAsync(userId, projectId);
            
            if (existingUserProject != null)
            {
                if (!existingUserProject.IsActive)
                {
                    existingUserProject.IsActive = true;
                    existingUserProject.JoinedAt = DateTime.UtcNow;
                    existingUserProject.RoleId = roleId;
                    _context.UserProjects.Update(existingUserProject);
                }
                else
                {
                    return false; // User already in project
                }
            }
            else
            {
                var userProject = new UserProject
                {
                    UserId = userId,
                    ProjectId = projectId,
                    RoleId = roleId,
                    JoinedAt = DateTime.UtcNow,
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow
                };
                
                await _context.UserProjects.AddAsync(userProject);
            }

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> RemoveUserFromProjectAsync(int userId, int projectId)
        {
            var userProject = await GetByUserAndProjectAsync(userId, projectId);
            
            if (userProject == null || !userProject.IsActive)
                return false;

            userProject.IsActive = false;
            userProject.LastModifiedAt = DateTime.UtcNow;
            
            _context.UserProjects.Update(userProject);
            await _context.SaveChangesAsync();
            
            return true;
        }

        public async Task<bool> UpdateUserRoleInProjectAsync(int userId, int projectId, int? roleId)
        {
            var userProject = await GetByUserAndProjectAsync(userId, projectId);
            
            if (userProject == null || !userProject.IsActive)
                return false;

            userProject.RoleId = roleId;
            userProject.LastModifiedAt = DateTime.UtcNow;
            
            _context.UserProjects.Update(userProject);
            await _context.SaveChangesAsync();
            
            return true;
        }
    }
}