using CleanArchitectureAPI.Domain.Interfaces;
using CleanArchitectureAPI.Entities.Issue;
using CleanArchitectureAPI.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;

namespace CleanArchitectureAPI.Infrastructure.Repositories
{
    public class WorklogRepository : IWorklogRepository
    {
        private readonly ApplicationDbContext _context;

        public WorklogRepository(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<Worklog> CreateAsync(Worklog worklog)
        {
            _context.Worklogs.Add(worklog);
            await _context.SaveChangesAsync();
            return worklog;
        }

        public async Task<Worklog?> GetByIdAsync(int id)
        {
            return await _context.Worklogs
                .Include(w => w.Author)
                .Include(w => w.Issue)
                .FirstOrDefaultAsync(w => w.Id == id);
        }

        public async Task<IEnumerable<Worklog>> GetByIssueIdAsync(int issueId)
        {
            return await _context.Worklogs
                .Include(w => w.Author)
                .Where(w => w.IssueId == issueId)
                .OrderByDescending(w => w.StartDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Worklog>> GetByAuthorIdAsync(int authorId)
        {
            return await _context.Worklogs
                .Include(w => w.Issue)
                .Where(w => w.AuthorId == authorId)
                .OrderByDescending(w => w.StartDate)
                .ToListAsync();
        }

        public async Task<Worklog> UpdateAsync(Worklog worklog)
        {
            worklog.IsEdited = true;
            worklog.LastEditedAt = DateTime.UtcNow;
            _context.Worklogs.Update(worklog);
            await _context.SaveChangesAsync();
            return worklog;
        }

        public async Task<bool> DeleteAsync(int id)
        {
            var worklog = await _context.Worklogs.FindAsync(id);
            if (worklog == null) return false;

            _context.Worklogs.Remove(worklog);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> ExistsAsync(int id)
        {
            return await _context.Worklogs.AnyAsync(w => w.Id == id);
        }

        public async Task<IEnumerable<Worklog>> GetPagedAsync(int issueId, int pageNumber, int pageSize)
        {
            return await _context.Worklogs
                .Include(w => w.Author)
                .Where(w => w.IssueId == issueId)
                .OrderByDescending(w => w.StartDate)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();
        }

        public async Task<int> GetCountByIssueIdAsync(int issueId)
        {
            return await _context.Worklogs.CountAsync(w => w.IssueId == issueId);
        }

        public async Task<IEnumerable<Worklog>> GetByDateRangeAsync(int issueId, DateTime fromDate, DateTime toDate)
        {
            return await _context.Worklogs
                .Include(w => w.Author)
                .Where(w => w.IssueId == issueId && 
                           w.StartDate >= fromDate && 
                           w.StartDate <= toDate)
                .OrderByDescending(w => w.StartDate)
                .ToListAsync();
        }

        public async Task<int> GetTotalTimeWorkedAsync(int issueId)
        {
            return await _context.Worklogs
                .Where(w => w.IssueId == issueId)
                .SumAsync(w => w.TimeWorkedSeconds);
        }

        public async Task<int> GetTotalTimeWorkedByAuthorAsync(int issueId, int authorId)
        {
            return await _context.Worklogs
                .Where(w => w.IssueId == issueId && w.AuthorId == authorId)
                .SumAsync(w => w.TimeWorkedSeconds);
        }

        public async Task<IEnumerable<Worklog>> GetByAuthorAndDateRangeAsync(int authorId, DateTime fromDate, DateTime toDate)
        {
            return await _context.Worklogs
                .Include(w => w.Issue)
                .Where(w => w.AuthorId == authorId && 
                           w.StartDate >= fromDate && 
                           w.StartDate <= toDate)
                .OrderByDescending(w => w.StartDate)
                .ToListAsync();
        }

        public async Task<Dictionary<int, int>> GetTimeWorkedByIssueAsync(IEnumerable<int> issueIds)
        {
            return await _context.Worklogs
                .Where(w => issueIds.Contains(w.IssueId))
                .GroupBy(w => w.IssueId)
                .ToDictionaryAsync(g => g.Key, g => g.Sum(w => w.TimeWorkedSeconds));
        }
    }
}
