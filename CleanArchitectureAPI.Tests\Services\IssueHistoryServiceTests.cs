using Xunit;
using Moq;
using CleanArchitectureAPI.Application.Services;
using CleanArchitectureAPI.Application.Interfaces;
using CleanArchitectureAPI.Domain.Interfaces;
using CleanArchitectureAPI.Application.DTOs;
using CleanArchitectureAPI.Entities.Issue;
using CleanArchitectureAPI.Domain.Entities.User;

namespace CleanArchitectureAPI.Tests.Services
{
    public class IssueHistoryServiceTests
    {
        private readonly Mock<ICommentRepository> _mockCommentRepository;
        private readonly Mock<IChangeGroupRepository> _mockChangeGroupRepository;
        private readonly Mock<IChangeItemRepository> _mockChangeItemRepository;
        private readonly Mock<ICurrentUserService> _mockCurrentUserService;
        private readonly IssueHistoryService _service;

        public IssueHistoryServiceTests()
        {
            _mockCommentRepository = new Mock<ICommentRepository>();
            _mockChangeGroupRepository = new Mock<IChangeGroupRepository>();
            _mockChangeItemRepository = new Mock<IChangeItemRepository>();
            _mockCurrentUserService = new Mock<ICurrentUserService>();

            _service = new IssueHistoryService(
                _mockCommentRepository.Object,
                _mockChangeGroupRepository.Object,
                _mockChangeItemRepository.Object,
                _mockCurrentUserService.Object
            );
        }

        [Fact]
        public async Task CreateCommentAsync_ShouldCreateComment_WhenValidData()
        {
            // Arrange
            var createCommentDto = new CreateCommentDto
            {
                IssueId = 1,
                ActionBody = "Test comment",
                CommentType = "User"
            };

            var expectedComment = new Comment
            {
                Id = 1,
                IssueId = 1,
                ActionBody = "Test comment",
                CommentType = "User",
                AuthorId = 123,
                Issue = null!,
                Author = null!
            };

            _mockCurrentUserService.Setup(x => x.GetUserId()).Returns(123);
            _mockCommentRepository.Setup(x => x.CreateAsync(It.IsAny<Comment>()))
                .ReturnsAsync(expectedComment);

            // Act
            var result = await _service.CreateCommentAsync(createCommentDto);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedComment.Id, result.Id);
            Assert.Equal(expectedComment.ActionBody, result.ActionBody);
            Assert.Equal(expectedComment.CommentType, result.CommentType);
            _mockCommentRepository.Verify(x => x.CreateAsync(It.IsAny<Comment>()), Times.Once);
        }

        [Fact]
        public async Task GetCommentsByIssueIdAsync_ShouldReturnComments_WhenIssueExists()
        {
            // Arrange
            var issueId = 1;
            var comments = new List<Comment>
            {
                new Comment
                {
                    Id = 1,
                    IssueId = issueId,
                    ActionBody = "Comment 1",
                    CommentType = "User",
                    AuthorId = 123,
                    Author = new AppUser { FirstName = "John", LastName = "Doe" },
                    Issue = null!
                },
                new Comment
                {
                    Id = 2,
                    IssueId = issueId,
                    ActionBody = "Comment 2",
                    CommentType = "System",
                    AuthorId = 124,
                    Author = new AppUser { FirstName = "Jane", LastName = "Smith" },
                    Issue = null!
                }
            };

            _mockCommentRepository.Setup(x => x.GetCommentsByIssueIdAsync(issueId))
                .ReturnsAsync(comments);

            // Act
            var result = await _service.GetCommentsByIssueIdAsync(issueId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count());
            Assert.Equal("Comment 1", result.First().ActionBody);
            Assert.Equal("John Doe", result.First().AuthorName);
        }

        [Fact]
        public async Task CreateChangeGroupAsync_ShouldCreateChangeGroupWithItems_WhenValidData()
        {
            // Arrange
            var createChangeGroupDto = new CreateChangeGroupDto
            {
                IssueId = 1,
                Description = "Status changed",
                ChangeType = "Manual",
                ChangeItems = new List<CreateChangeItemDto>
                {
                    new CreateChangeItemDto
                    {
                        FieldType = "jira",
                        Field = "status",
                        FieldDisplayName = "Status",
                        OldValue = "1",
                        OldString = "Open",
                        NewValue = "2",
                        NewString = "In Progress"
                    }
                }
            };

            var expectedChangeGroup = new ChangeGroup
            {
                Id = 1,
                IssueId = 1,
                Description = "Status changed",
                ChangeType = "Manual",
                AuthorId = 123,
                Issue = null!,
                Author = null!
            };

            _mockCurrentUserService.Setup(x => x.GetUserId()).Returns(123);
            _mockChangeGroupRepository.Setup(x => x.CreateAsync(It.IsAny<ChangeGroup>()))
                .ReturnsAsync(expectedChangeGroup);

            // Act
            var result = await _service.CreateChangeGroupAsync(createChangeGroupDto);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedChangeGroup.Id, result.Id);
            Assert.Equal(expectedChangeGroup.Description, result.Description);
            _mockChangeGroupRepository.Verify(x => x.CreateAsync(It.IsAny<ChangeGroup>()), Times.Once);
            _mockChangeItemRepository.Verify(x => x.CreateManyAsync(It.IsAny<IEnumerable<ChangeItem>>()), Times.Once);
        }

        [Fact]
        public async Task TrackIssueChangesAsync_ShouldCreateChangeGroupAndItems_WhenChangesProvided()
        {
            // Arrange
            var issueId = 1;
            var changes = new Dictionary<string, (object? oldValue, object? newValue, string? displayName)>
            {
                { "status", ("1", "2", "Status") },
                { "assignee", ("user1", "user2", "Assignee") }
            };

            var expectedChangeGroup = new ChangeGroup
            {
                Id = 1,
                IssueId = issueId,
                Description = "Issue updated",
                ChangeType = "Manual",
                AuthorId = 123,
                Issue = null!,
                Author = null!
            };

            _mockCurrentUserService.Setup(x => x.GetUserId()).Returns(123);
            _mockChangeGroupRepository.Setup(x => x.CreateAsync(It.IsAny<ChangeGroup>()))
                .ReturnsAsync(expectedChangeGroup);

            // Act
            var result = await _service.TrackIssueChangesAsync(issueId, changes, "Issue updated");

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedChangeGroup.Id, result.Id);
            Assert.Equal("Issue updated", result.Description);
            _mockChangeGroupRepository.Verify(x => x.CreateAsync(It.IsAny<ChangeGroup>()), Times.Once);
            _mockChangeItemRepository.Verify(x => x.CreateManyAsync(It.IsAny<IEnumerable<ChangeItem>>()), Times.Once);
        }

        [Fact]
        public async Task UpdateCommentAsync_ShouldUpdateComment_WhenCommentExists()
        {
            // Arrange
            var commentId = 1;
            var updateCommentDto = new UpdateCommentDto
            {
                ActionBody = "Updated comment",
                CommentType = "User"
            };

            var existingComment = new Comment
            {
                Id = commentId,
                IssueId = 1,
                ActionBody = "Original comment",
                CommentType = "User",
                AuthorId = 123,
                Issue = null!,
                Author = null!
            };

            _mockCurrentUserService.Setup(x => x.GetUserId()).Returns(123);
            _mockCommentRepository.Setup(x => x.GetByIdAsync(commentId))
                .ReturnsAsync(existingComment);
            _mockCommentRepository.Setup(x => x.UpdateAsync(It.IsAny<Comment>()))
                .ReturnsAsync(existingComment);

            // Act
            var result = await _service.UpdateCommentAsync(commentId, updateCommentDto);

            // Assert
            Assert.NotNull(result);
            Assert.Equal("Updated comment", result.ActionBody);
            _mockCommentRepository.Verify(x => x.UpdateAsync(It.IsAny<Comment>()), Times.Once);
        }

        [Fact]
        public async Task DeleteCommentAsync_ShouldReturnTrue_WhenCommentExists()
        {
            // Arrange
            var commentId = 1;
            var existingComment = new Comment
            {
                Id = commentId,
                IssueId = 1,
                ActionBody = "Test comment",
                CommentType = "User",
                AuthorId = 123,
                Issue = null!,
                Author = null!
            };

            _mockCommentRepository.Setup(x => x.GetByIdAsync(commentId))
                .ReturnsAsync(existingComment);
            _mockCommentRepository.Setup(x => x.DeleteAsync(commentId))
                .ReturnsAsync(true);

            // Act
            var result = await _service.DeleteCommentAsync(commentId);

            // Assert
            Assert.True(result);
            _mockCommentRepository.Verify(x => x.DeleteAsync(commentId), Times.Once);
        }

        [Fact]
        public async Task GetIssueHistoryAsync_ShouldReturnCombinedHistory_WhenDataExists()
        {
            // Arrange
            var issueId = 1;
            var comments = new List<Comment>
            {
                new Comment
                {
                    Id = 1,
                    IssueId = issueId,
                    ActionBody = "Comment 1",
                    CommentType = "User",
                    AuthorId = 123,
                    CreatedAt = DateTime.UtcNow.AddHours(-2),
                    Author = new AppUser { FirstName = "John", LastName = "Doe" },
                    Issue = null!
                }
            };

            var changeGroups = new List<ChangeGroup>
            {
                new ChangeGroup
                {
                    Id = 1,
                    IssueId = issueId,
                    Description = "Status changed",
                    ChangeType = "Manual",
                    AuthorId = 124,
                    CreatedAt = DateTime.UtcNow.AddHours(-1),
                    Author = new AppUser { FirstName = "Jane", LastName = "Smith" },
                    Issue = null!
                }
            };

            _mockCommentRepository.Setup(x => x.GetCommentsByIssueIdAsync(issueId))
                .ReturnsAsync(comments);
            _mockChangeGroupRepository.Setup(x => x.GetChangeGroupsByIssueIdAsync(issueId))
                .ReturnsAsync(changeGroups);

            // Act
            var result = await _service.GetIssueHistoryAsync(issueId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count());
            
            // Should be ordered by CreatedAt descending
            var orderedResult = result.OrderByDescending(x => x.CreatedAt).ToList();
            Assert.Equal("ChangeGroup", orderedResult[0].Type);
            Assert.Equal("Comment", orderedResult[1].Type);
        }
    }
}
