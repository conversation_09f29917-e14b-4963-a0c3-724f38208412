using Xunit;
using Moq;
using CleanArchitectureAPI.Application.Services;
using CleanArchitectureAPI.Application.Interfaces;
using CleanArchitectureAPI.Domain.Interfaces;
using CleanArchitectureAPI.Application.DTOs;
using CleanArchitectureAPI.Entities.Issue;
using CleanArchitectureAPI.Domain.Entities.User;

namespace CleanArchitectureAPI.Tests.Services
{
    public class WorklogServiceTests
    {
        private readonly Mock<IWorklogRepository> _mockWorklogRepository;
        private readonly Mock<ICurrentUserService> _mockCurrentUserService;
        private readonly WorklogService _service;

        public WorklogServiceTests()
        {
            _mockWorklogRepository = new Mock<IWorklogRepository>();
            _mockCurrentUserService = new Mock<ICurrentUserService>();

            _service = new WorklogService(
                _mockWorklogRepository.Object,
                _mockCurrentUserService.Object
            );
        }

        [Fact]
        public async Task CreateWorklogAsync_ShouldCreateWorklog_WhenValidData()
        {
            // Arrange
            var createWorklogDto = new CreateWorklogDto
            {
                IssueId = 1,
                WorklogBody = "Worked on bug fix",
                StartDate = DateTime.UtcNow.AddHours(-2),
                EndDate = DateTime.UtcNow,
                TimeWorkedSeconds = 7200, // 2 hours
                WorklogType = "Manual",
                IsPublic = true
            };

            var expectedWorklog = new Worklog
            {
                Id = 1,
                IssueId = 1,
                WorklogBody = "Worked on bug fix",
                StartDate = createWorklogDto.StartDate,
                EndDate = createWorklogDto.EndDate,
                TimeWorkedSeconds = 7200,
                WorklogType = "Manual",
                IsPublic = true,
                AuthorId = 123,
                Issue = null!,
                Author = null!
            };

            _mockCurrentUserService.Setup(x => x.GetUserId()).Returns(123);
            _mockWorklogRepository.Setup(x => x.CreateAsync(It.IsAny<Worklog>()))
                .ReturnsAsync(expectedWorklog);

            // Act
            var result = await _service.CreateWorklogAsync(createWorklogDto);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedWorklog.Id, result.Id);
            Assert.Equal(expectedWorklog.WorklogBody, result.WorklogBody);
            Assert.Equal(expectedWorklog.TimeWorkedSeconds, result.TimeWorkedSeconds);
            _mockWorklogRepository.Verify(x => x.CreateAsync(It.IsAny<Worklog>()), Times.Once);
        }

        [Fact]
        public async Task GetWorklogsByIssueIdAsync_ShouldReturnWorklogs_WhenIssueExists()
        {
            // Arrange
            var issueId = 1;
            var worklogs = new List<Worklog>
            {
                new Worklog
                {
                    Id = 1,
                    IssueId = issueId,
                    WorklogBody = "First worklog",
                    TimeWorkedSeconds = 3600,
                    AuthorId = 123,
                    Author = new AppUser { FirstName = "John", LastName = "Doe" },
                    Issue = null!
                },
                new Worklog
                {
                    Id = 2,
                    IssueId = issueId,
                    WorklogBody = "Second worklog",
                    TimeWorkedSeconds = 1800,
                    AuthorId = 124,
                    Author = new AppUser { FirstName = "Jane", LastName = "Smith" },
                    Issue = null!
                }
            };

            _mockWorklogRepository.Setup(x => x.GetWorklogsByIssueIdAsync(issueId))
                .ReturnsAsync(worklogs);

            // Act
            var result = await _service.GetWorklogsByIssueIdAsync(issueId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count());
            Assert.Equal("First worklog", result.First().WorklogBody);
            Assert.Equal("John Doe", result.First().AuthorName);
        }

        [Fact]
        public async Task UpdateWorklogAsync_ShouldUpdateWorklog_WhenWorklogExists()
        {
            // Arrange
            var worklogId = 1;
            var updateWorklogDto = new UpdateWorklogDto
            {
                WorklogBody = "Updated worklog description",
                StartDate = DateTime.UtcNow.AddHours(-3),
                TimeWorkedSeconds = 10800, // 3 hours
                WorklogType = "Manual"
            };

            var existingWorklog = new Worklog
            {
                Id = worklogId,
                IssueId = 1,
                WorklogBody = "Original worklog",
                TimeWorkedSeconds = 7200,
                AuthorId = 123,
                Issue = null!,
                Author = null!
            };

            _mockCurrentUserService.Setup(x => x.GetUserId()).Returns(123);
            _mockWorklogRepository.Setup(x => x.GetByIdAsync(worklogId))
                .ReturnsAsync(existingWorklog);
            _mockWorklogRepository.Setup(x => x.UpdateAsync(It.IsAny<Worklog>()))
                .ReturnsAsync(existingWorklog);

            // Act
            var result = await _service.UpdateWorklogAsync(worklogId, updateWorklogDto);

            // Assert
            Assert.NotNull(result);
            Assert.Equal("Updated worklog description", result.WorklogBody);
            _mockWorklogRepository.Verify(x => x.UpdateAsync(It.IsAny<Worklog>()), Times.Once);
        }

        [Fact]
        public async Task DeleteWorklogAsync_ShouldReturnTrue_WhenWorklogExists()
        {
            // Arrange
            var worklogId = 1;
            var existingWorklog = new Worklog
            {
                Id = worklogId,
                IssueId = 1,
                WorklogBody = "Test worklog",
                TimeWorkedSeconds = 3600,
                AuthorId = 123,
                Issue = null!,
                Author = null!
            };

            _mockWorklogRepository.Setup(x => x.GetByIdAsync(worklogId))
                .ReturnsAsync(existingWorklog);
            _mockWorklogRepository.Setup(x => x.DeleteAsync(worklogId))
                .ReturnsAsync(true);

            // Act
            var result = await _service.DeleteWorklogAsync(worklogId);

            // Assert
            Assert.True(result);
            _mockWorklogRepository.Verify(x => x.DeleteAsync(worklogId), Times.Once);
        }

        [Fact]
        public async Task GetWorklogsByAuthorIdAsync_ShouldReturnAuthorWorklogs_WhenAuthorExists()
        {
            // Arrange
            var authorId = 123;
            var worklogs = new List<Worklog>
            {
                new Worklog
                {
                    Id = 1,
                    IssueId = 1,
                    WorklogBody = "Author worklog 1",
                    TimeWorkedSeconds = 3600,
                    AuthorId = authorId,
                    Author = new AppUser { FirstName = "John", LastName = "Doe" },
                    Issue = null!
                },
                new Worklog
                {
                    Id = 2,
                    IssueId = 2,
                    WorklogBody = "Author worklog 2",
                    TimeWorkedSeconds = 1800,
                    AuthorId = authorId,
                    Author = new AppUser { FirstName = "John", LastName = "Doe" },
                    Issue = null!
                }
            };

            _mockWorklogRepository.Setup(x => x.GetWorklogsByAuthorIdAsync(authorId))
                .ReturnsAsync(worklogs);

            // Act
            var result = await _service.GetWorklogsByAuthorIdAsync(authorId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count());
            Assert.All(result, w => Assert.Equal("John Doe", w.AuthorName));
        }

        [Fact]
        public async Task GetIssueWorklogStatisticsAsync_ShouldReturnStatistics_WhenWorklogsExist()
        {
            // Arrange
            var issueId = 1;
            var worklogs = new List<Worklog>
            {
                new Worklog
                {
                    Id = 1,
                    IssueId = issueId,
                    TimeWorkedSeconds = 3600, // 1 hour
                    AuthorId = 123,
                    CreatedAt = DateTime.UtcNow.AddDays(-2),
                    Issue = null!,
                    Author = null!
                },
                new Worklog
                {
                    Id = 2,
                    IssueId = issueId,
                    TimeWorkedSeconds = 7200, // 2 hours
                    AuthorId = 124,
                    CreatedAt = DateTime.UtcNow.AddDays(-1),
                    Issue = null!,
                    Author = null!
                }
            };

            _mockWorklogRepository.Setup(x => x.GetWorklogsByIssueIdAsync(issueId))
                .ReturnsAsync(worklogs);

            // Act
            var result = await _service.GetIssueWorklogStatisticsAsync(issueId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(10800, result["totalTimeWorked"]); // 3 hours total
            Assert.Equal(2, result["worklogCount"]);
            Assert.Equal(5400, result["averageTimePerWorklog"]); // 1.5 hours average
            Assert.Equal(2, result["uniqueAuthors"]);
        }

        [Fact]
        public async Task CanUserEditWorklogAsync_ShouldReturnTrue_WhenUserIsAuthor()
        {
            // Arrange
            var worklogId = 1;
            var userId = 123;
            var worklog = new Worklog
            {
                Id = worklogId,
                AuthorId = userId,
                Issue = null!,
                Author = null!
            };

            _mockWorklogRepository.Setup(x => x.GetByIdAsync(worklogId))
                .ReturnsAsync(worklog);

            // Act
            var result = await _service.CanUserEditWorklogAsync(worklogId, userId);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public async Task CanUserEditWorklogAsync_ShouldReturnFalse_WhenUserIsNotAuthor()
        {
            // Arrange
            var worklogId = 1;
            var userId = 123;
            var differentUserId = 456;
            var worklog = new Worklog
            {
                Id = worklogId,
                AuthorId = differentUserId,
                Issue = null!,
                Author = null!
            };

            _mockWorklogRepository.Setup(x => x.GetByIdAsync(worklogId))
                .ReturnsAsync(worklog);

            // Act
            var result = await _service.CanUserEditWorklogAsync(worklogId, userId);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task IsWorklogValidAsync_ShouldReturnTrue_WhenValidWorklog()
        {
            // Arrange
            var createWorklogDto = new CreateWorklogDto
            {
                IssueId = 1,
                WorklogBody = "Valid worklog",
                StartDate = DateTime.UtcNow.AddHours(-2),
                EndDate = DateTime.UtcNow,
                TimeWorkedSeconds = 7200,
                WorklogType = "Manual"
            };

            // Act
            var result = await _service.IsWorklogValidAsync(createWorklogDto);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public async Task IsWorklogValidAsync_ShouldReturnFalse_WhenInvalidTimeWorked()
        {
            // Arrange
            var createWorklogDto = new CreateWorklogDto
            {
                IssueId = 1,
                WorklogBody = "Invalid worklog",
                StartDate = DateTime.UtcNow.AddHours(-2),
                EndDate = DateTime.UtcNow,
                TimeWorkedSeconds = 0, // Invalid: zero time worked
                WorklogType = "Manual"
            };

            // Act
            var result = await _service.IsWorklogValidAsync(createWorklogDto);

            // Assert
            Assert.False(result);
        }
    }
}
