# Chỉ Mục Dự Án Clean Architecture API

## 📋 Tổng Quan Dự Án
Đây là một giải pháp .NET 8 Clean Architecture triển khai hệ thống quản lý dự án với xác thực JWT, quản lý người dùng và khả năng theo dõi vấn đề (issue tracking).

## 🏗️ Cấu Trúc Giải Pháp

### Các Dự Án Chính

#### 1. **CleanArchitecture.API** (Lớp Trình Bày)
- **M<PERSON><PERSON> đích**: Điểm vào Web API, controllers và xử lý HTTP
- **Thành phần chính**:
  - Controllers: Auth, Categories, Projects, Status
  - Middlewares: Xử lý ngoại lệ
  - DTOs: Đ<PERSON>i tượng truyền dữ liệu Request/Response
  - Extensions: Tiện ích ánh xạ kết quả

#### 2. **CleanArchitectureAPI.Application** (Lớp Ứng Dụng)
- **<PERSON><PERSON>c đích**: Logic nghiệp vụ, use cases và dịch vụ ứng dụng
- **Thành phần chính**:
  - Services: Auth, AppUser, Category, Project, Status, ProjectAccess
  - DTOs: Đối tượng truyền dữ liệu đặc thù ứng dụng
  - Requests: Đối tượng yêu cầu Command/Query
  - Validators: Quy tắc FluentValidation
  - Interfaces: Hợp đồng dịch vụ

#### 3. **CleanArchitectureAPI.Domain** (Lớp Miền)
- **Mục đích**: Thực thể nghiệp vụ cốt lõi, quy tắc và logic miền
- **Thành phần chính**:
  - Entities: Đối tượng nghiệp vụ cốt lõi
  - Interfaces: Hợp đồng repository
  - Exceptions: Ngoại lệ đặc thù miền
  - Enums: Liệt kê miền

#### 4. **CleanArchitectureAPI.Infrastructure** (Lớp Hạ Tầng)
- **Mục đích**: Truy cập dữ liệu, dịch vụ bên ngoài và các vấn đề hạ tầng
- **Thành phần chính**:
  - Data: Entity Framework DbContext và cấu hình
  - Repositories: Triển khai truy cập dữ liệu
  - Auth: Tạo token JWT
  - Migrations: Thay đổi schema cơ sở dữ liệu

#### 5. **CleanArchitecture.Shared** (Lớp Chia Sẻ)
- **Mục đích**: Tiện ích và helper chung
- **Thành phần chính**:
  - Helpers: Tiện ích JWT, Password, Cookie, Audit

## 🗂️ Chỉ Mục Thành Phần Chi Tiết

### Thực Thể Miền

#### Thực Thể Nghiệp Vụ Cốt Lõi
- **Project**: Thực thể dự án chính với cài đặt và metadata
- **AppUser**: Quản lý người dùng với vai trò, quyền và nhóm
- **Issue**: Theo dõi vấn đề với danh mục, cột mốc, phiên bản
- **Category**: Phân loại dự án
- **Status**: Quản lý trạng thái vấn đề
- **Priority**: Mức độ ưu tiên vấn đề
- **IssueType**: Các loại vấn đề
- **Milestone**: Cột mốc dự án
- **Version**: Phiên bản dự án
- **Resolution**: Các loại giải quyết vấn đề

#### Thực Thể Quản Lý Người Dùng
- **Group**: Nhóm người dùng
- **Role**: Vai trò người dùng
- **Permission**: Quyền hệ thống
- **UserGroup**: Mối quan hệ người dùng-nhóm
- **UserRole**: Mối quan hệ người dùng-vai trò
- **UserPermission**: Mối quan hệ người dùng-quyền
- **RolePermission**: Mối quan hệ vai trò-quyền
- **GroupPermission**: Mối quan hệ nhóm-quyền

#### Thực Thể Quản Lý Vấn Đề
- **Comment**: Bình luận vấn đề
- **FileAttachment**: Tệp đính kèm vấn đề
- **Worklog**: Nhật ký công việc vấn đề
- **ChangeGroup**: Nhóm thay đổi vấn đề
- **ChangeItem**: Mục thay đổi vấn đề
- **IssueCategory**: Mối quan hệ vấn đề-danh mục
- **IssueMilestone**: Mối quan hệ vấn đề-cột mốc
- **IssueVersion**: Mối quan hệ vấn đề-phiên bản

### Dịch Vụ Ứng Dụng

#### Xác Thực & Phân Quyền
- **AuthService**: Đăng nhập, đăng ký, quản lý token
- **AppUserServices**: Thao tác CRUD người dùng
- **CurrentUserService**: Ngữ cảnh người dùng hiện tại

#### Quản Lý Dự Án
- **ProjectService**: Thao tác CRUD dự án
- **ProjectAccessService**: Kiểm soát truy cập dự án
- **CategoryService**: Quản lý danh mục
- **StatusService**: Quản lý trạng thái

### Thành Phần Hạ Tầng

#### Truy Cập Dữ Liệu
- **ApplicationDbContext**: Ngữ cảnh Entity Framework
- **Repository Pattern**: Repository chung và đặc thù
- **Entity Configurations**: Cấu hình Fluent API

#### Xác Thực
- **JwtTokenGenerator**: Tạo và xác thực token JWT
- **JwtSettings**: Cấu hình JWT

### API Controllers

#### Xác Thực
- **AuthController**: Đăng nhập, đăng ký, làm mới token

#### Quản Lý Dự Án
- **ProjectsController**: Thao tác CRUD dự án
- **CategoriesController**: Quản lý danh mục
- **StatusController**: Quản lý trạng thái

### DTOs Request/Response

#### Xác Thực
- **LoginRequestDTO**: Thông tin đăng nhập
- **RegisterRequestDTO**: Dữ liệu đăng ký
- **AuthDTO**: Phản hồi xác thực

#### Quản Lý Dự Án
- **CreateProjectRequest**: Tạo dự án
- **UpdateProjectRequest**: Cập nhật dự án
- **CreateCategoryRequest**: Tạo danh mục
- **UpdateCategoryRequest**: Cập nhật danh mục
- **CreateStatusRequest**: Tạo trạng thái
- **UpdateStatusRequest**: Cập nhật trạng thái

### Xác Thực

#### Bộ Xác Thực Xác Thực
- **LoginUserRequestValidator**: Quy tắc xác thực đăng nhập
- **RegisterUserRequestValidator**: Quy tắc xác thực đăng ký

#### Bộ Xác Thực Quản Lý Dự Án
- **CreateProjectRequestValidator**: Xác thực tạo dự án
- **CreateCategoryRequestValidator**: Xác thực tạo danh mục
- **UpdateCategoryRequestValidator**: Xác thực cập nhật danh mục
- **CreateStatusRequestValidator**: Xác thực tạo trạng thái
- **UpdateStatusRequestValidator**: Xác thực cập nhật trạng thái

## 🔧 Stack Công Nghệ

### Công Nghệ Cốt Lõi
- **.NET 8**: Phiên bản framework
- **Entity Framework Core**: ORM
- **SQL Server**: Cơ sở dữ liệu
- **JWT**: Xác thực
- **FluentValidation**: Xác thực đầu vào
- **Mapster**: Ánh xạ đối tượng
- **Swagger/OpenAPI**: Tài liệu API

### Mẫu Kiến Trúc
- **Clean Architecture**: Tách biệt các mối quan tâm
- **Repository Pattern**: Trừu tượng hóa truy cập dữ liệu
- **CQRS**: Tách biệt trách nhiệm Command Query
- **Dependency Injection**: Quản lý dịch vụ
- **Middleware Pattern**: Các mối quan tâm xuyên suốt

## 🚀 Bắt Đầu

### Yêu Cầu
- .NET 8 SDK
- SQL Server
- Visual Studio 2022 hoặc VS Code

### Cấu Hình
1. Cập nhật connection string trong `appsettings.json`
2. Cấu hình cài đặt JWT
3. Chạy database migrations

### Chạy Ứng Dụng
```bash
cd CleanArchitecture.API
dotnet run
```

## 📚 Tài Liệu API
- Swagger UI có sẵn tại `/swagger` khi chạy ở chế độ Development
- Xác thực JWT cần thiết cho các endpoint được bảo vệ
- CORS được cấu hình cho `http://localhost:3000`

## 🔐 Tính Năng Bảo Mật
- Xác thực dựa trên JWT
- Phân quyền dựa trên vai trò
- Mã hóa mật khẩu
- Bảo vệ CORS
- Middleware xử lý ngoại lệ

## 📁 Tóm Tắt Cấu Trúc Tệp

```
CleanArchitecture.API/
├── Controllers/          # Các endpoint API
├── DTOs/                # Đối tượng Request/Response
├── Middlewares/         # Các mối quan tâm xuyên suốt
├── Extension/           # Tiện ích mở rộng
└── Program.cs           # Điểm vào ứng dụng

CleanArchitectureAPI.Application/
├── Services/            # Logic nghiệp vụ
├── DTOs/               # DTOs ứng dụng
├── Requests/           # Đối tượng Command/Query
├── Validators/         # Quy tắc xác thực
└── Interfaces/         # Hợp đồng dịch vụ

CleanArchitectureAPI.Domain/
├── Entities/           # Thực thể nghiệp vụ
├── Interfaces/         # Hợp đồng repository
├── Exceptions/         # Ngoại lệ miền
└── Enums/             # Liệt kê miền

CleanArchitectureAPI.Infrastructure/
├── Data/              # Ngữ cảnh cơ sở dữ liệu
├── Repositories/      # Truy cập dữ liệu
├── Auth/              # Xác thực
├── Migrations/        # Di chuyển cơ sở dữ liệu
└── Settings/          # Cấu hình

CleanArchitecture.Shared/
└── Helpers/           # Tiện ích chung
```

## 🎯 Tính Năng Chính
- ✅ Xác thực và phân quyền người dùng
- ✅ Quản lý dự án
- ✅ Theo dõi vấn đề
- ✅ Kiểm soát truy cập dựa trên vai trò
- ✅ Tệp đính kèm
- ✅ Bình luận và nhật ký công việc
- ✅ Quản lý cột mốc và phiên bản
- ✅ Quản lý danh mục và trạng thái
- ✅ Ghi log kiểm toán
- ✅ Tài liệu API

## 🔄 Quy Trình Phát Triển
1. Thực thể miền định nghĩa quy tắc nghiệp vụ
2. Dịch vụ ứng dụng triển khai use cases
3. Hạ tầng xử lý lưu trữ dữ liệu
4. API controllers phơi bày endpoints
5. Xác thực đảm bảo tính toàn vẹn dữ liệu
6. Middleware xử lý các mối quan tâm xuyên suốt



Chỉ mục này cung cấp cái nhìn tổng quan toàn diện về cấu trúc và thành phần dự án Clean Architecture API để dễ dàng điều hướng và hiểu biết.
CleanArchitectureAPI.Application/
├── Services/IssueTypeService.cs
│   └── 
│       public class IssueTypeService : IIssueTypeService
│       {
│           private readonly IIssueTypeRepository _issueTypeRepository;
│           private readonly ICurrentUserService _currentUserService;
│           private readonly IProjectAccessService _projectAccessService;
│
│           public IssueTypeService(IIssueTypeRepository issueTypeRepository, ICurrentUserService currentUserService, IProjectAccessService projectAccessService)
│           {
│               _issueTypeRepository = issueTypeRepository;
│               _currentUserService = currentUserService;
│               _projectAccessService = projectAccessService;
│           }
│
│           public async Task<Result<IssueTypeDto>> CreateAsync(CreateIssueTypeRequest request)
│           {
│               int? projectId = await _projectAccessService.ResolveProjectIdAsync(request.ProjectKey);
│               if (projectId is null)
│                   return Result<IssueTypeDto>.Failure(ErrorCode.NotFound, "Project không tồn tại");
│
│               var isNameDuplicated = await _issueTypeRepository.IsNameDuplicated(request.ProjectKey, request.Name);
│               if (isNameDuplicated)
│                   return Result<IssueTypeDto>.Failure(ErrorCode.Conflict, "Tên loại issue đã tồn tại trong dự án này.");
│
│               var issueType = new IssueType
│               {
│                   Name = request.Name,
│                   Description = request.Description,
│                   Color = request.Color,
│                   ProjectKey = request.ProjectKey,
│                   Order = await _issueTypeRepository.GetMaxOrderInProjectAsync(request.ProjectKey) + 1,
│                   ProjectId = projectId.Value,
│                   CreatedById = _currentUserService.GetUserId(),
│                   IsActive = true,
│                   CreatedAt = DateTime.UtcNow,
│               };
│
│               await _issueTypeRepository.CreateAsync(issueType);
│               return Result<IssueTypeDto>.Success(issueType.Adapt<IssueTypeDto>());
│           }
│
│           public async Task<Result<bool>> UpdateAsync(int id, UpdateIssueTypeRequest request)
│           {
│               var issueType = await _issueTypeRepository.GetByIdAsync(id);
│               if (issueType == null)
│                   return Result<bool>.Failure("Không tìm thấy loại issue.");
│
│               var existingTypes = await _issueTypeRepository.GetAllByProjectKeyAsync(request.ProjectKey);
│               var isNameDuplicated = existingTypes
│                   .Where(t => t.Id != id && t.ProjectKey == issueType.ProjectKey && t.Name == request.Name)
│                   .Any();
│               if (isNameDuplicated)
│                   return Result<bool>.Failure("Tên loại issue đã tồn tại trong dự án này.");
│
│               issueType.Name = request.Name;
│               issueType.Description = request.Description;
│               issueType.Color = request.Color;
│               issueType.LastModifiedAt = DateTime.UtcNow;
│               issueType.LastModifiedById = _currentUserService.GetUserId();
│               issueType.IsActive = request.IsActive;
│
│               await _issueTypeRepository.UpdateAsync(issueType);
│               return Result<bool>.Success(true);
│           }
│
│           public async Task<Result<bool>> ReorderAsync(ReorderIssueTypeRequest request)
│           {
│               if (string.IsNullOrWhiteSpace(request.ProjectKey) || request.IssueTypeIdsInOrder == null || !request.IssueTypeIdsInOrder.Any())
│                   return Result<bool>.Failure("Dữ liệu không hợp lệ.");
│
│               await _issueTypeRepository.ReorderIssueTypesAsync(request.ProjectKey, request.IssueTypeIdsInOrder);
│               return Result<bool>.Success(true);
│           }
│
│           public async Task<Result<bool>> SoftDeleteAsync(int id)
│           {
│               var issueType = await _issueTypeRepository.GetByIdAsync(id);
│               if (issueType == null)
│                   return Result<bool>.Failure("Không tìm thấy loại issue.");
│
│               issueType.DeletedById = _currentUserService.GetUserId();
│               issueType.DeletedAt = DateTime.UtcNow;
│               await _issueTypeRepository.SoftDeleteAsync(id);
│               return Result<bool>.Success(true);
│           }
│       }
├── DTOs/IssueTypeDto.cs
│   └── 
│       public class IssueTypeDto
│       {
│           public int Id { get; set; }
│           public string Name { get; set; }
│           public string Description { get; set; }
│           public string Color { get; set; }
│           public string ProjectKey { get; set; }
│           public int Order { get; set; }
│           public bool IsActive { get; set; }
│           public DateTime CreatedAt { get; set; }
│           public DateTime? LastModifiedAt { get; set; }
│       }
├── Requests/IssueTypeRequests/
│   ├── CreateIssueTypeRequest.cs
│   │   └── 
│   │       public class CreateIssueTypeRequest
│   │       {
│   │           public string Name { get; set; }
│   │           public string Description { get; set; }
│   │           public string Color { get; set; }
│   │           public string ProjectKey { get; set; }
│   │       }
│   ├── UpdateIssueTypeRequest.cs
│   │   └── 
│   │       public class UpdateIssueTypeRequest
│   │       {
│   │           public string Name { get; set; }
│   │           public string Description { get; set; }
│   │           public string Color { get; set; }
│   │           public bool IsActive { get; set; }
│   │           public string ProjectKey { get; set; }
│   │       }
│   └── ReorderIssueTypeRequest.cs
│       └── 
│           public class ReorderIssueTypeRequest
│           {
│               public string ProjectKey { get; set; }
│               public List<int> IssueTypeIdsInOrder { get; set; }
│           }
├── Validators/IssueType/
│   ├── CreateIssueTypeRequestValidator.cs
│   │   └── 
│   │       public class CreateIssueTypeRequestValidator : AbstractValidator<CreateIssueTypeRequest>
│   │       {
│   │           public CreateIssueTypeRequestValidator()
│   │           {
│   │               RuleFor(x => x.Name).NotEmpty().MaximumLength(100);
│   │               RuleFor(x => x.ProjectKey).NotEmpty().MaximumLength(20);
│   │           }
│   │       }
│   └── UpdateIssueTypeRequestValidator.cs
│       └── 
│           public class UpdateIssueTypeRequestValidator : AbstractValidator<UpdateIssueTypeRequest>
│           {
│               public UpdateIssueTypeRequestValidator()
│               {
│                   RuleFor(x => x.Name).NotEmpty().MaximumLength(100);
│                   RuleFor(x => x.ProjectKey).NotEmpty().MaximumLength(20);
│               }
│           }
└── Interfaces/IIssueTypeService.cs
    └── 
        public interface IIssueTypeService
        {
            Task<Result<IssueTypeDto>> CreateAsync(CreateIssueTypeRequest request);
            Task<Result<bool>> UpdateAsync(int id, UpdateIssueTypeRequest request);
            Task<Result<bool>> ReorderAsync(ReorderIssueTypeRequest request);
            Task<Result<bool>> SoftDeleteAsync(int id);
        }

CleanArchitectureAPI.Domain/
├── Entities/IssueType.cs
│   └── 
│       public class IssueType
│       {
│           public int Id { get; set; }
│           public string Name { get; set; }
│           public string Description { get; set; }
│           public string Color { get; set; }
│           public string ProjectKey { get; set; }
│           public int ProjectId { get; set; }
│           public int Order { get; set; }
│           public bool IsActive { get; set; }
│           public DateTime CreatedAt { get; set; }
│           public int CreatedById { get; set; }
│           public DateTime? LastModifiedAt { get; set; }
│           public int? LastModifiedById { get; set; }
│           public DateTime? DeletedAt { get; set; }
│           public int? DeletedById { get; set; }
│       }
├── Interfaces/IIssueTypeRepository.cs
│   └── 
│       public interface IIssueTypeRepository
│       {
│           Task CreateAsync(IssueType issueType);
│           Task UpdateAsync(IssueType issueType);
│           Task<IssueType> GetByIdAsync(int id);
│           Task<IEnumerable<IssueType>> GetAllByProjectKeyAsync(string projectKey);
│           Task<bool> IsNameDuplicated(string projectKey, string name);
│           Task<int> GetMaxOrderInProjectAsync(string projectKey);
│           Task ReorderIssueTypesAsync(string projectKey, List<int> issueTypeIdsInOrder);
│           Task SoftDeleteAsync(int id);
│       }

CleanArchitectureAPI.Infrastructure/
└── Repositories/IssueTypeRepository.cs
    └── 
        public class IssueTypeRepository : IIssueTypeRepository
        {
            private readonly AppDbContext _context;
            public IssueTypeRepository(AppDbContext context)
            {
                _context = context;
            }
            public async Task CreateAsync(IssueType issueType)
            {
                _context.IssueTypes.Add(issueType);
                await _context.SaveChangesAsync();
            }
            public async Task UpdateAsync(IssueType issueType)
            {
                _context.IssueTypes.Update(issueType);
                await _context.SaveChangesAsync();
            }
            public async Task<IssueType> GetByIdAsync(int id)
            {
                return await _context.IssueTypes.FindAsync(id);
            }
            public async Task<IEnumerable<IssueType>> GetAllByProjectKeyAsync(string projectKey)
            {
                return await _context.IssueTypes
                    .Where(x => x.ProjectKey == projectKey && x.DeletedAt == null)
                    .ToListAsync();
            }
            public async Task<bool> IsNameDuplicated(string projectKey, string name)
            {
                return await _context.IssueTypes
                    .AnyAsync(x => x.ProjectKey == projectKey && x.Name == name && x.DeletedAt == null);
            }
            public async Task<int> GetMaxOrderInProjectAsync(string projectKey)
            {
                return await _context.IssueTypes
                    .Where(x => x.ProjectKey == projectKey && x.DeletedAt == null)
                    .MaxAsync(x => (int?)x.Order) ?? 0;
            }
            public async Task ReorderIssueTypesAsync(string projectKey, List<int> issueTypeIdsInOrder)
            {
                var issueTypes = await _context.IssueTypes
                    .Where(x => x.ProjectKey == projectKey && x.DeletedAt == null)
                    .ToListAsync();
                for (int i = 0; i < issueTypeIdsInOrder.Count; i++)
                {
                    var issueType = issueTypes.FirstOrDefault(x => x.Id == issueTypeIdsInOrder[i]);
                    if (issueType != null)
                        issueType.Order = i + 1;
                }
                await _context.SaveChangesAsync();
            }
            public async Task SoftDeleteAsync(int id)
            {
                var issueType = await _context.IssueTypes.FindAsync(id);
                if (issueType != null)
                {
                    issueType.DeletedAt = DateTime.UtcNow;
                    _context.IssueTypes.Update(issueType);
                    await _context.SaveChangesAsync();
                }
            }
        }

 Category

 IssueType

 Milestone

 Priority


 Resolution


 Version