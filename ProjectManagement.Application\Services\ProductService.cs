﻿using CleanArchitectureAPI.Application.DTOs;
using CleanArchitectureAPI.Application.Interfaces;
using CleanArchitectureAPI.Domain.Entities;
using CleanArchitectureAPI.Domain.Interfaces;
using CleanArchitectureAPI.Domain.Exceptions;

namespace CleanArchitectureAPI.Application.Services
{
	public class ProductService : IProductService
	{
		private readonly IProductRepository _productRepository;

		public ProductService(IProductRepository productRepository)
		{
			_productRepository = productRepository;
		}

		public async Task<IEnumerable<ProductDto>> GetAllProductsAsync()
		{
			var products = await _productRepository.GetAllAsync();
			return products.Select(MapToDto);
		}

		public async Task<ProductDto> GetProductByIdAsync(int id)
		{
			var product = await _productRepository.GetByIdAsync(id);
			if (product == null)
				throw new ProductNotFoundException(id);

			return MapToDto(product);
		}

		public async Task<ProductDto> CreateProductAsync(CreateProductDto createProductDto)
		{
			var product = new Product
			{
				Name = createProductDto.Name,
				Description = createProductDto.Description,
				Price = createProductDto.Price,
				Quantity = createProductDto.Quantity,
				CreatedAt = DateTime.UtcNow,
				IsActive = true
			};

			var createdProduct = await _productRepository.CreateAsync(product);
			return MapToDto(createdProduct);
		}

		public async Task<ProductDto> UpdateProductAsync(int id, UpdateProductDto updateProductDto)
		{
			var existingProduct = await _productRepository.GetByIdAsync(id);
			if (existingProduct == null)
				throw new ProductNotFoundException(id);

			existingProduct.Name = updateProductDto.Name;
			existingProduct.Description = updateProductDto.Description;
			existingProduct.Price = updateProductDto.Price;
			existingProduct.Quantity = updateProductDto.Quantity;
			existingProduct.IsActive = updateProductDto.IsActive;
			existingProduct.UpdatedAt = DateTime.UtcNow;

			var updatedProduct = await _productRepository.UpdateAsync(existingProduct);
			return MapToDto(updatedProduct);
		}

		public async Task<bool> DeleteProductAsync(int id)
		{
			var exists = await _productRepository.ExistsAsync(id);
			if (!exists)
				throw new ProductNotFoundException(id);

			return await _productRepository.DeleteAsync(id);
		}

		private static ProductDto MapToDto(Product product)
		{
			return new ProductDto
			{
				Id = product.Id,
				Name = product.Name,
				Description = product.Description,
				Price = product.Price,
				Quantity = product.Quantity,
				IsActive = product.IsActive
			};
		}
	}
}
