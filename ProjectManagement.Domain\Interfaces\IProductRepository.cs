﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CleanArchitectureAPI.Domain.Entities;

namespace CleanArchitectureAPI.Domain.Interfaces
{
	public interface IProductRepository
	{
		Task<IEnumerable<Product>> GetAllAsync();
		Task<Product?> GetByIdAsync(int id);
		Task<Product> CreateAsync(Product product);
		Task<Product> UpdateAsync(Product product);
		Task<bool> DeleteAsync(int id);
		Task<bool> ExistsAsync(int id);
	}
}
