﻿using CleanArchitectureAPI.Domain.Entities;
using Microsoft.EntityFrameworkCore;

namespace CleanArchitectureAPI.Infrastructure.Data
{
	public class ApplicationDbContext : DbContext
	{
		public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
		{
		}

		public DbSet<Product> Products { get; set; }

		protected override void OnModelCreating(ModelBuilder modelBuilder)
		{
			modelBuilder.Entity<Product>(entity =>
			{
				entity.HasKey(e => e.Id);
				entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
				entity.Property(e => e.Description).HasMaxLength(500);
				entity.Property(p => p.Price).HasColumnType("decimal(18,2)");
				entity.Property(e => e.CreatedAt).IsRequired();
			});

			base.OnModelCreating(modelBuilder);
		}
	}
}
