# Issue Tracking và Worklog System

## Tổng quan

Đã hoàn thành việc implement hệ thống theo dõi thay đổi issue và worklog cho Clean Architecture API project với các chức năng sau:

## 🎯 Chức năng đã hoàn thành

### 1. **Issue History Tracking**
- **Comment System**: <PERSON><PERSON> <PERSON>hận và quản lý comments cho issues
- **Change Tracking**: <PERSON> dõi các thay đổi field của issue theo thời gian
- **Change Groups**: Nhóm các thay đổi liên quan với nhau
- **Change Items**: Chi tiết từng field thay đổi (old value → new value)

### 2. **Worklog Management**
- **Time Tracking**: <PERSON><PERSON> nhận thời gian làm việc trên issue
- **Worklog CRUD**: Tạo, đọc, cập nhật, xóa worklog entries
- **Statistics**: Thống kê thời gian làm việc theo issue, author, date range
- **Permission Control**: Chỉ author mớ<PERSON> c<PERSON> thể edit/delete worklog của mình

## 🏗️ Kiến trúc đã implement

### **Domain Layer** (`CleanArchitectureAPI.Domain`)
```
Entities/Issue/
├── Comment.cs          # Comments trên issues
├── ChangeGroup.cs      # Nhóm các thay đổi
├── ChangeItem.cs       # Chi tiết thay đổi từng field
├── Worklog.cs          # Time tracking entries
└── Issue.cs            # Issue entity (đã có sẵn)
```

### **Application Layer** (`CleanArchitectureAPI.Application`)
```
DTOs/
├── CommentDto.cs       # Comment data transfer objects
├── ChangeGroupDto.cs   # Change group DTOs
├── ChangeItemDto.cs    # Change item DTOs
└── WorklogDto.cs       # Worklog DTOs

Services/
├── IssueHistoryService.cs  # Business logic cho issue history
└── WorklogService.cs       # Business logic cho worklog

Interfaces/
├── IIssueHistoryService.cs
└── IWorklogService.cs

Validators/
├── Comment/            # FluentValidation validators
├── ChangeGroup/
├── ChangeItem/
└── Worklog/
```

### **Infrastructure Layer** (`CleanArchitectureAPI.Infrastructure`)
```
Repositories/
├── CommentRepository.cs
├── ChangeGroupRepository.cs
├── ChangeItemRepository.cs
└── WorklogRepository.cs

Data/
└── ApplicationDbContext.cs  # EF Core configuration
```

### **API Layer** (`CleanArchitecture.API`)
```
Controllers/
├── IssueHistoryController.cs  # REST endpoints cho issue history
└── WorklogController.cs       # REST endpoints cho worklog
```

## 📊 Database Schema

### **Comments Table**
- Lưu trữ comments và system messages
- Liên kết với Issue và Author
- Support cho different comment types (User, System, etc.)

### **ChangeGroups Table**
- Nhóm các thay đổi liên quan (ví dụ: một lần update issue)
- Metadata: author, timestamp, description, change type

### **ChangeItems Table**
- Chi tiết từng field thay đổi
- Old value/string và New value/string
- Field type và display name

### **Worklogs Table**
- Time tracking entries
- Start/end time, time worked (seconds)
- Remaining estimate
- Public/private visibility
- Edit tracking

## 🔧 API Endpoints

### **Issue History Controller**
```
GET    /api/issuehistory/issue/{issueId}/comments
POST   /api/issuehistory/comments
PUT    /api/issuehistory/comments/{id}
DELETE /api/issuehistory/comments/{id}

GET    /api/issuehistory/issue/{issueId}/changegroups
POST   /api/issuehistory/changegroups
PUT    /api/issuehistory/changegroups/{id}

POST   /api/issuehistory/issue/{issueId}/track-changes
GET    /api/issuehistory/issue/{issueId}/field-history/{fieldName}
GET    /api/issuehistory/issue/{issueId}/history
```

### **Worklog Controller**
```
GET    /api/worklog/issue/{issueId}
POST   /api/worklog
PUT    /api/worklog/{id}
DELETE /api/worklog/{id}

GET    /api/worklog/my-worklogs
GET    /api/worklog/my-worklogs/date-range
GET    /api/worklog/my-worklogs/statistics

GET    /api/worklog/issue/{issueId}/statistics
GET    /api/worklog/author/{authorId}/statistics
```

## ✅ Features

### **Comment System**
- ✅ CRUD operations cho comments
- ✅ Link comments với change groups
- ✅ Support multiple comment types
- ✅ Edit tracking (last edited time/user)

### **Change Tracking**
- ✅ Automatic change detection
- ✅ Field-level change history
- ✅ Grouped changes (change groups)
- ✅ Old/new value tracking
- ✅ Custom field support

### **Worklog System**
- ✅ Time tracking với precision (seconds)
- ✅ Start/end time recording
- ✅ Remaining estimate tracking
- ✅ Public/private worklogs
- ✅ Edit permission control
- ✅ Statistics và reporting

### **Validation**
- ✅ FluentValidation cho tất cả DTOs
- ✅ Business rule validation
- ✅ Data integrity checks

### **Security**
- ✅ User-based permissions
- ✅ Author-only edit/delete
- ✅ Current user context

## 🚀 Cách sử dụng

### **1. Track Issue Changes**
```csharp
var changes = new Dictionary<string, (object? oldValue, object? newValue, string? displayName)>
{
    { "status", ("1", "2", "Status") },
    { "assignee", ("user1", "user2", "Assignee") }
};

await issueHistoryService.TrackIssueChangesAsync(issueId, changes, "Issue updated");
```

### **2. Add Comment**
```csharp
var comment = new CreateCommentDto
{
    IssueId = 1,
    ActionBody = "This is a comment",
    CommentType = "User"
};

await issueHistoryService.CreateCommentAsync(comment);
```

### **3. Log Work**
```csharp
var worklog = new CreateWorklogDto
{
    IssueId = 1,
    WorklogBody = "Fixed the bug",
    StartDate = DateTime.UtcNow.AddHours(-2),
    TimeWorkedSeconds = 7200, // 2 hours
    WorklogType = "Manual"
};

await worklogService.CreateWorklogAsync(worklog);
```

## 📈 Statistics Available

### **Issue Statistics**
- Total time worked
- Number of worklogs
- Average time per worklog
- Unique contributors
- First/last worklog dates

### **Author Statistics**
- Personal time tracking
- Date range filtering
- Worklog count and averages

## 🔄 Migration

Migration đã được tạo: `UpdateIssueTrackingEntities`

Để apply migration:
```bash
dotnet ef database update --project CleanArchitectureAPI.Infrastructure --startup-project CleanArchitecture.API
```

## 🧪 Testing

Unit tests đã được tạo trong `CleanArchitectureAPI.Tests`:
- `IssueHistoryServiceTests.cs`
- `WorklogServiceTests.cs`

## 📝 Notes

1. **Database Connection**: Cần đảm bảo SQL Server đang chạy và connection string đúng
2. **Authentication**: System sử dụng current user context để track author
3. **Permissions**: Chỉ author mới có thể edit/delete own content
4. **Time Precision**: Worklog time được lưu bằng seconds để chính xác
5. **Extensibility**: Design cho phép extend thêm field types và change types

## 🎉 Kết luận

Hệ thống issue tracking và worklog đã được implement hoàn chỉnh với:
- ✅ Clean Architecture principles
- ✅ Comprehensive API endpoints  
- ✅ Proper validation và error handling
- ✅ Database relationships và constraints
- ✅ Security và permissions
- ✅ Statistics và reporting
- ✅ Unit tests coverage

System sẵn sàng để sử dụng và có thể extend thêm features theo nhu cầu!
